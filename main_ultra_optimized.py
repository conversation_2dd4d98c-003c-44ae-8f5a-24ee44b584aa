#!/usr/bin/env python3
"""
Shell.ai Hackathon 2025: ULTRA-OPTIMIZED Fuel Blend Properties Prediction
Focused on maximum performance with efficient training

Key Optimizations:
- Target-specific feature engineering based on analysis
- Optimized LightGBM with target-specific parameters
- Advanced weighted averaging and interactions
- Efficient ensemble with 2 complementary models
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, KFold
from sklearn.multioutput import MultiOutputRegressor
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_percentage_error
from sklearn.preprocessing import RobustScaler
from sklearn.feature_selection import SelectKBest, f_regression
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load the training, test, and sample solution data"""
    print("✔️ Loading data files...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_solution = pd.read_csv('sample_solution.csv')
    
    print(f"   - Training data shape: {train_df.shape}")
    print(f"   - Test data shape: {test_df.shape}")
    print(f"   - Sample solution shape: {sample_solution.shape}")
    
    return train_df, test_df, sample_solution

def ultra_feature_engineering(df, fraction_cols, property_cols, is_train=True):
    """Ultra-optimized feature engineering based on analysis insights"""
    print("✔️ Applying ULTRA-OPTIMIZED feature engineering...")
    
    df_features = df.copy()
    
    # 1. Enhanced weighted averages (most important features)
    print("   - Creating enhanced weighted features...")
    for prop_num in range(1, 11):
        weighted_sum = 0
        squared_weighted_sum = 0
        
        for comp_num in range(1, 6):
            fraction_col = f'Component{comp_num}_fraction'
            property_col = f'Component{comp_num}_Property{prop_num}'
            
            if fraction_col in df.columns and property_col in df.columns:
                # Linear weighting
                weighted_sum += df[fraction_col] * df[property_col]
                # Quadratic weighting (emphasizes dominant components)
                squared_weighted_sum += (df[fraction_col] ** 2) * df[property_col]
        
        df_features[f'WeightedAvg_Property{prop_num}'] = weighted_sum
        df_features[f'QuadWeightedAvg_Property{prop_num}'] = squared_weighted_sum
    
    # 2. Component fraction statistics (key predictors)
    print("   - Creating component statistics...")
    df_features['Fraction_Variance'] = df[fraction_cols].var(axis=1)
    df_features['Fraction_Std'] = df[fraction_cols].std(axis=1)
    df_features['Fraction_Range'] = df[fraction_cols].max(axis=1) - df[fraction_cols].min(axis=1)
    
    # 3. Dominant component analysis (highly predictive)
    df_features['Dominant_Component'] = df[fraction_cols].idxmax(axis=1).str.extract(r'(\d+)').astype(int)
    df_features['Max_Fraction'] = df[fraction_cols].max(axis=1)
    df_features['Second_Max_Fraction'] = df[fraction_cols].apply(lambda x: x.nlargest(2).iloc[1], axis=1)
    df_features['Dominance_Ratio'] = df_features['Max_Fraction'] / (df_features['Second_Max_Fraction'] + 1e-10)
    
    # 4. Component diversity (Shannon entropy)
    fractions_array = df[fraction_cols].values + 1e-10
    df_features['Shannon_Entropy'] = -np.sum(fractions_array * np.log(fractions_array), axis=1)
    
    # 5. Property statistics for key properties
    print("   - Creating property statistics...")
    key_properties = [1, 2, 3, 5, 7, 8, 10]  # Most predictive based on analysis
    
    for prop_num in key_properties:
        prop_cols = [f'Component{i}_Property{prop_num}' for i in range(1, 6)]
        available_cols = [col for col in prop_cols if col in df.columns]
        
        if available_cols:
            df_features[f'Property{prop_num}_Mean'] = df[available_cols].mean(axis=1)
            df_features[f'Property{prop_num}_Std'] = df[available_cols].std(axis=1)
            df_features[f'Property{prop_num}_Range'] = df[available_cols].max(axis=1) - df[available_cols].min(axis=1)
    
    # 6. Key interactions based on correlation analysis
    print("   - Creating key interactions...")
    # BlendProperty3 ↔ BlendProperty7 (correlation: 0.997)
    df_features['WeightedAvg_P3_P7_Product'] = (
        df_features['WeightedAvg_Property3'] * df_features['WeightedAvg_Property7']
    )
    
    # BlendProperty1 ↔ BlendProperty4 (correlation: 0.743)
    df_features['WeightedAvg_P1_P4_Product'] = (
        df_features['WeightedAvg_Property1'] * df_features['WeightedAvg_Property4']
    )
    
    # Component-specific interactions for most important components
    important_components = [2, 4, 5]  # Based on correlation analysis
    
    for comp in important_components:
        fraction_col = f'Component{comp}_fraction'
        if fraction_col in df.columns:
            # Key property interactions
            for prop in [1, 2, 5, 7]:
                property_col = f'Component{comp}_Property{prop}'
                if property_col in df.columns:
                    df_features[f'C{comp}_F_P{prop}_Interaction'] = df[fraction_col] * df[property_col]
                    df_features[f'C{comp}_F2_P{prop}_Interaction'] = (df[fraction_col] ** 2) * df[property_col]
    
    # 7. Blend characteristics
    df_features['Total_Blend_Volume'] = df[fraction_cols].sum(axis=1)
    df_features['Volume_Deviation'] = abs(df_features['Total_Blend_Volume'] - 1.0)
    df_features['Effective_Components'] = 1 / np.sum(df[fraction_cols].values ** 2, axis=1)
    
    print(f"   - Original features: {df.shape[1]}")
    print(f"   - Engineered features: {df_features.shape[1]}")
    print(f"   - New features added: {df_features.shape[1] - df.shape[1]}")
    
    return df_features

def create_ultra_ensemble():
    """Create optimized ensemble with 2 complementary models"""
    print("✔️ Creating ultra-optimized ensemble...")
    
    # Model 1: Highly tuned LightGBM
    lgb_model = lgb.LGBMRegressor(
        objective='regression',
        metric='mae',
        boosting_type='gbdt',
        num_leaves=31,
        learning_rate=0.03,
        feature_fraction=0.8,
        bagging_fraction=0.8,
        bagging_freq=5,
        verbose=-1,
        random_state=42,
        n_estimators=800,
        max_depth=8,
        min_child_samples=20,
        reg_alpha=0.1,
        reg_lambda=0.1,
        min_split_gain=0.01,
        subsample_for_bin=200000
    )
    
    # Model 2: Random Forest for diversity
    rf_model = RandomForestRegressor(
        n_estimators=300,
        max_depth=12,
        min_samples_split=5,
        min_samples_leaf=2,
        max_features='sqrt',
        random_state=42,
        n_jobs=-1
    )
    
    return {'lgb': lgb_model, 'rf': rf_model}

class UltraEnsemble:
    """Ultra-efficient ensemble with optimized weighting"""
    
    def __init__(self, models):
        self.models = models
        self.fitted_models = {}
        self.weights = None
        self.scaler = RobustScaler()
        
    def fit(self, X, y):
        """Fit ensemble models"""
        print("   - Training ultra ensemble...")
        
        # Scale features for Random Forest
        X_scaled = self.scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        predictions = {}
        
        # Fit LightGBM (works well with raw features)
        print("     → Training LightGBM...")
        lgb_multi = MultiOutputRegressor(self.models['lgb'], n_jobs=1)
        lgb_multi.fit(X, y)
        self.fitted_models['lgb'] = lgb_multi
        predictions['lgb'] = lgb_multi.predict(X)
        
        # Fit Random Forest (works well with scaled features)
        print("     → Training Random Forest...")
        rf_multi = MultiOutputRegressor(self.models['rf'], n_jobs=1)
        rf_multi.fit(X_scaled_df, y)
        self.fitted_models['rf'] = rf_multi
        predictions['rf'] = rf_multi.predict(X_scaled_df)
        
        # Calculate optimal weights
        self._calculate_weights(predictions, y)
        
    def _calculate_weights(self, predictions, y_true):
        """Calculate optimal ensemble weights"""
        print("   - Calculating optimal weights...")
        
        n_targets = y_true.shape[1]
        self.weights = np.zeros((n_targets, 2))  # 2 models
        
        for target_idx in range(n_targets):
            lgb_pred = predictions['lgb'][:, target_idx]
            rf_pred = predictions['rf'][:, target_idx]
            target_true = y_true.iloc[:, target_idx].values
            
            # Calculate MAPE for each model
            lgb_mape = mean_absolute_percentage_error(target_true, lgb_pred)
            rf_mape = mean_absolute_percentage_error(target_true, rf_pred)
            
            # Weight by inverse MAPE
            inv_mapes = np.array([1.0 / (lgb_mape + 1e-10), 1.0 / (rf_mape + 1e-10)])
            self.weights[target_idx] = inv_mapes / inv_mapes.sum()
        
        # Display weights
        print("   - Ensemble weights by target:")
        for target_idx in range(n_targets):
            lgb_w, rf_w = self.weights[target_idx]
            print(f"     Target {target_idx+1}: LGB={lgb_w:.3f} | RF={rf_w:.3f}")
    
    def predict(self, X):
        """Make ensemble predictions"""
        # Scale features for RF
        X_scaled = self.scaler.transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=X.columns, index=X.index)
        
        # Get predictions
        lgb_pred = self.fitted_models['lgb'].predict(X)
        rf_pred = self.fitted_models['rf'].predict(X_scaled_df)
        
        # Weighted ensemble
        n_samples, n_targets = lgb_pred.shape
        ensemble_pred = np.zeros((n_samples, n_targets))
        
        for target_idx in range(n_targets):
            lgb_w, rf_w = self.weights[target_idx]
            ensemble_pred[:, target_idx] = (
                lgb_w * lgb_pred[:, target_idx] + rf_w * rf_pred[:, target_idx]
            )
        
        return ensemble_pred

def calculate_mape_score(y_true, y_pred, reference_cost):
    """Calculate leaderboard score using MAPE and reference cost"""
    mape = mean_absolute_percentage_error(y_true, y_pred)
    score = max(10, 100 - (90 * mape / reference_cost))
    return score, mape

def cross_validate_model(X, y, model, cv_folds=3):
    """Quick cross-validation"""
    print("✔️ Performing cross-validation...")

    kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
    cv_scores = []

    for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
        X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
        y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]

        model.fit(X_train_fold, y_train_fold)
        y_pred_fold = model.predict(X_val_fold)

        fold_mape = mean_absolute_percentage_error(y_val_fold.values, y_pred_fold)
        cv_scores.append(fold_mape)

    mean_cv_score = np.mean(cv_scores)
    print(f"   - CV MAPE: {mean_cv_score:.4f}")

    return mean_cv_score

def main():
    """Main execution function with ultra optimization"""
    print("🚀 Shell.ai Hackathon 2025: ULTRA-OPTIMIZED Fuel Blend Properties Prediction")
    print("=" * 80)

    # Load data
    train_df, test_df, sample_solution = load_data()

    # Identify feature columns
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    property_cols = [col for col in train_df.columns if 'Property' in col and 'Blend' not in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]

    print(f"✔️ Data overview:")
    print(f"   - Component fractions: {len(fraction_cols)}")
    print(f"   - Component properties: {len(property_cols)}")
    print(f"   - Blend properties (targets): {len(target_cols)}")

    # Ultra Feature Engineering
    train_features = ultra_feature_engineering(train_df, fraction_cols, property_cols, is_train=True)
    test_features = ultra_feature_engineering(test_df, fraction_cols, property_cols, is_train=False)

    # Prepare features and targets
    print("✔️ Preparing features and targets...")

    # Remove target columns from features
    feature_columns = [col for col in train_features.columns if col not in target_cols]
    X = train_features[feature_columns]
    y = train_features[target_cols]

    # Ensure test features have same columns as training features
    X_test = test_features[feature_columns]

    print(f"   - Feature matrix shape: {X.shape}")
    print(f"   - Target matrix shape: {y.shape}")
    print(f"   - Test matrix shape: {X_test.shape}")

    # Handle missing values and infinite values
    print("✔️ Cleaning data...")
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    X_test = X_test.replace([np.inf, -np.inf], np.nan)
    X_test = X_test.fillna(X.median())

    # Feature selection to keep most important features
    print("✔️ Performing feature selection...")
    selector = SelectKBest(score_func=f_regression, k=min(150, X.shape[1]))
    X_selected = pd.DataFrame(
        selector.fit_transform(X, y.iloc[:, 0]),  # Use first target for selection
        columns=X.columns[selector.get_support()],
        index=X.index
    )
    X_test_selected = pd.DataFrame(
        selector.transform(X_test),
        columns=X.columns[selector.get_support()],
        index=X_test.index
    )

    print(f"   - Selected {X_selected.shape[1]} features from {X.shape[1]}")

    # Split training data
    X_train, X_val, y_train, y_val = train_test_split(
        X_selected, y, test_size=0.2, random_state=42
    )

    print(f"   - Training set: {X_train.shape[0]} samples")
    print(f"   - Validation set: {X_val.shape[0]} samples")

    # Create and train ultra ensemble
    base_models = create_ultra_ensemble()
    ensemble = UltraEnsemble(base_models)

    # Train ensemble
    ensemble.fit(X_train, y_train)

    # Cross-validation
    cv_score = cross_validate_model(X_selected, y, ensemble, cv_folds=3)

    # Make predictions on validation set
    print("✔️ Evaluating model performance...")
    y_val_pred = ensemble.predict(X_val)

    # Calculate individual target performance
    target_mapes = []
    for i in range(y_val.shape[1]):
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_val_pred[:, i])
        target_mapes.append(mape)

    avg_mape = np.mean(target_mapes)
    print(f"   - Average validation MAPE: {avg_mape:.4f}")

    # Calculate leaderboard scores
    public_score, public_mape = calculate_mape_score(y_val.values, y_val_pred, 2.72)
    private_score, private_mape = calculate_mape_score(y_val.values, y_val_pred, 2.58)

    print(f"   - Public leaderboard score: {public_score:.2f} (MAPE: {public_mape:.4f})")
    print(f"   - Private leaderboard score: {private_score:.2f} (MAPE: {private_mape:.4f})")

    # Make predictions on test set
    print("✔️ Making predictions on test set...")
    test_predictions = ensemble.predict(X_test_selected)

    # Create submission file
    print("✔️ Creating ultra-optimized submission file...")
    submission_df = pd.DataFrame()
    submission_df['ID'] = test_df['ID']

    for i, target_col in enumerate(target_cols):
        submission_df[target_col] = test_predictions[:, i]

    # Save submission
    submission_df.to_csv('submission_ultra.csv', index=False)
    print(f"   - Ultra submission saved as 'submission_ultra.csv'")
    print(f"   - Submission shape: {submission_df.shape}")

    # Final summary
    print("\n" + "=" * 80)
    print("🎯 ULTRA-OPTIMIZED RESULTS SUMMARY")
    print("=" * 80)
    print(f"✅ Ultra ensemble trained with {X_selected.shape[1]} features")
    print(f"✅ Cross-validation MAPE: {cv_score:.4f}")
    print(f"✅ Validation MAPE: {avg_mape:.4f}")
    print(f"✅ Public score: {public_score:.2f} | Private score: {private_score:.2f}")
    print(f"✅ Ultra submission created with {len(submission_df)} predictions")

    # Display individual target performance
    print("\n📊 Individual Target Performance (Validation MAPE):")
    target_performance = list(zip(target_cols, target_mapes))
    target_performance.sort(key=lambda x: x[1])  # Sort by MAPE

    for i, (target, mape) in enumerate(target_performance, 1):
        if mape < 0.5:
            status = "🔥 EXCELLENT"
        elif mape < 1.0:
            status = "⚡ GREAT"
        elif mape < 1.5:
            status = "📈 GOOD"
        else:
            status = "🎯 FOCUS"
        print(f"   {i:2d}. {target:<20} {mape:.4f} {status}")

    # Performance insights
    best_target = min(target_performance, key=lambda x: x[1])
    worst_target = max(target_performance, key=lambda x: x[1])

    print(f"\n💡 Performance Insights:")
    print(f"   🏆 Best target: {best_target[0]} (MAPE: {best_target[1]:.4f})")
    print(f"   🎯 Focus target: {worst_target[0]} (MAPE: {worst_target[1]:.4f})")
    print(f"   📊 Score improvement potential: {(2.0 - avg_mape) / 2.0 * 100:.1f}%")

    print(f"\n🏆 ULTRA-OPTIMIZED for Shell.ai Hackathon - Target Score: 90%+")

    return ensemble, submission_df

if __name__ == "__main__":
    ensemble, submission_df = main()
