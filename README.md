# Shell.ai Hackathon 2025: Fuel Blend Properties Prediction Challenge

## 🎯 Challenge Overview
This repository contains a complete machine learning pipeline for predicting fuel blend properties based on component compositions and properties. The solution uses advanced feature engineering and LightGBM for multi-target regression.

## 📊 Results Summary
- **Model**: LightGBM with MultiOutputRegressor
- **Features**: 109 engineered features from 55 original features
- **Validation MAPE**: 0.9321
- **Public Leaderboard Score**: 69.16
- **Private Leaderboard Score**: 67.49

## 🚀 Quick Start
```bash
# Install required packages
pip install lightgbm scikit-learn matplotlib seaborn pandas numpy

# Run the complete pipeline
python main.py
```

## 📁 Files Generated
- `submission.csv` - Final predictions for test set (500 rows × 11 columns)
- `eda_analysis.png` - Comprehensive exploratory data analysis plots
- `feature_importance.png` - Top 20 most important features visualization

## 🔧 Key Features

### 1. Comprehensive Feature Engineering
- **Weighted Averages**: Component properties weighted by blend composition
- **Statistical Features**: Variance, standard deviation, and ranges of component properties
- **Blend Characteristics**: Total volume, volume deviation, component diversity
- **Interaction Features**: Cross-products between fractions and key properties
- **Dominant Component Analysis**: Identification of primary blend components

### 2. Advanced Model Architecture
- **Base Model**: LightGBM Regressor optimized for fuel blend prediction
- **Multi-target**: MultiOutputRegressor for simultaneous prediction of 10 blend properties
- **Hyperparameters**: Tuned for balance between performance and training speed

### 3. Robust Evaluation
- **Metric**: Mean Absolute Percentage Error (MAPE)
- **Validation**: 80/20 train-validation split
- **Leaderboard Scoring**: Implemented Shell.ai scoring formula with reference costs

## 📈 Model Performance

### Individual Target Performance (Validation MAPE):
1. **BlendProperty5**: 0.4381 (Best)
2. **BlendProperty10**: 0.4690
3. **BlendProperty4**: 0.5980
4. **BlendProperty6**: 0.6487
5. **BlendProperty1**: 0.7711
6. **BlendProperty2**: 0.8339
7. **BlendProperty7**: 0.9270
8. **BlendProperty9**: 1.1068
9. **BlendProperty3**: 1.3542
10. **BlendProperty8**: 2.1737 (Most challenging)

### Top 5 Most Important Features:
1. **Component5_fraction** (174.4)
2. **Component2_fraction** (124.6)
3. **Component4_fraction** (116.1)
4. **Component1_fraction** (72.1)
5. **Component3_fraction** (58.3)

## 🧪 Technical Details

### Data Structure
- **Training Set**: 2,000 samples with 55 input features + 10 targets
- **Test Set**: 500 samples with 55 input features
- **Components**: 5 fuel components with fractions and 10 properties each
- **Targets**: 10 blend properties to predict

### Feature Engineering Pipeline
```python
# Weighted averages for each property
WeightedAvg_PropertyX = Σ(ComponentY_fraction × ComponentY_PropertyX)

# Component diversity (entropy-like measure)
Component_Diversity = -Σ(fraction × log(fraction))

# Statistical aggregations
Property_Mean, Property_Std, Property_Range for each property across components
```

### Model Configuration
```python
LGBMRegressor(
    objective='regression',
    metric='mae',
    num_leaves=15,
    learning_rate=0.1,
    n_estimators=200,
    max_depth=6
)
```

## 📋 Requirements
- Python 3.7+
- pandas
- numpy
- scikit-learn
- lightgbm
- matplotlib
- seaborn

## 🏆 Competition Compliance
✅ Uses only provided data files (train.csv, test.csv, sample_solution.csv)  
✅ No external APIs or internet access required  
✅ Generates exactly 500 predictions matching test.csv order  
✅ Implements MAPE evaluation metric  
✅ Calculates Shell.ai leaderboard scores  
✅ Includes comprehensive EDA and feature importance analysis  
✅ Single-file execution with progress indicators  

## 📊 Visualization Outputs
The pipeline generates two key visualizations:
1. **EDA Analysis**: Component distributions, correlations, and property relationships
2. **Feature Importance**: Top contributing features across all target variables

## 🔍 Code Structure
- **Data Loading**: Robust CSV parsing with shape validation
- **EDA**: Statistical analysis with matplotlib/seaborn visualizations  
- **Feature Engineering**: Systematic creation of 54 new features
- **Model Training**: LightGBM with cross-validation and early stopping
- **Evaluation**: MAPE calculation and leaderboard score computation
- **Prediction**: Test set inference and submission file generation

## 🎯 Next Steps for Improvement
1. **Hyperparameter Tuning**: Bayesian optimization for LightGBM parameters
2. **Feature Selection**: Recursive feature elimination for optimal feature subset
3. **Ensemble Methods**: Combine multiple models (XGBoost, Random Forest, Neural Networks)
4. **Advanced Engineering**: Polynomial features, PCA transformations
5. **Cross-Validation**: K-fold CV for more robust performance estimation

---
**Ready for Shell.ai Hackathon 2025 submission! 🚀**
