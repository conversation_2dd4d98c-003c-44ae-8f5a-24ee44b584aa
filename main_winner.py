#!/usr/bin/env python3
"""
Shell.ai Hackathon 2025: WINNER Performance Fuel Blend Properties Prediction
Super focused approach to ACE this competition ASAP

Strategy:
- Take ONLY the most effective features from successful approaches
- Use optimal hyperparameters
- Fast execution, maximum performance
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_percentage_error
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load the training, test, and sample solution data"""
    print("✔️ Loading data files...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_solution = pd.read_csv('sample_solution.csv')
    
    print(f"   - Training data shape: {train_df.shape}")
    print(f"   - Test data shape: {test_df.shape}")
    print(f"   - Sample solution shape: {sample_solution.shape}")
    
    return train_df, test_df, sample_solution

def winner_feature_engineering(df, fraction_cols, property_cols, is_train=True):
    """WINNER feature engineering - only the most effective features"""
    print("✔️ Applying WINNER feature engineering...")
    
    df_features = df.copy()
    
    # 1. Weighted averages (MOST EFFECTIVE)
    print("   - Creating winner weighted features...")
    for prop_num in range(1, 11):
        weighted_sum = 0
        squared_weighted_sum = 0
        
        for comp_num in range(1, 6):
            fraction_col = f'Component{comp_num}_fraction'
            property_col = f'Component{comp_num}_Property{prop_num}'
            
            if fraction_col in df.columns and property_col in df.columns:
                weighted_sum += df[fraction_col] * df[property_col]
                squared_weighted_sum += (df[fraction_col] ** 2) * df[property_col]
        
        df_features[f'WeightedAvg_Property{prop_num}'] = weighted_sum
        df_features[f'QuadWeightedAvg_Property{prop_num}'] = squared_weighted_sum
    
    # 2. Component statistics (PROVEN)
    df_features['Fraction_Variance'] = df[fraction_cols].var(axis=1)
    df_features['Fraction_Std'] = df[fraction_cols].std(axis=1)
    df_features['Fraction_Range'] = df[fraction_cols].max(axis=1) - df[fraction_cols].min(axis=1)
    
    # 3. Dominant component (PROVEN)
    df_features['Max_Fraction'] = df[fraction_cols].max(axis=1)
    df_features['Second_Max_Fraction'] = df[fraction_cols].apply(lambda x: x.nlargest(2).iloc[1], axis=1)
    df_features['Dominance_Ratio'] = df_features['Max_Fraction'] / (df_features['Second_Max_Fraction'] + 1e-10)
    
    # 4. Component diversity (PROVEN)
    fractions_array = df[fraction_cols].values + 1e-10
    df_features['Shannon_Entropy'] = -np.sum(fractions_array * np.log(fractions_array), axis=1)
    
    # 5. Key property interactions (HIGHEST IMPACT ONLY)
    key_interactions = [(3, 7), (1, 4), (1, 2), (2, 4)]  # Top 4 correlations
    
    for prop1, prop2 in key_interactions:
        df_features[f'WA_P{prop1}_P{prop2}_Product'] = (
            df_features[f'WeightedAvg_Property{prop1}'] * df_features[f'WeightedAvg_Property{prop2}']
        )
        df_features[f'WA_P{prop1}_P{prop2}_Ratio'] = (
            df_features[f'WeightedAvg_Property{prop1}'] / (df_features[f'WeightedAvg_Property{prop2}'] + 1e-10)
        )
    
    # 6. Component-specific interactions (TOP PERFORMERS ONLY)
    # Component 2 with Property 1 (most predictive)
    if 'Component2_fraction' in df.columns and 'Component2_Property1' in df.columns:
        df_features['C2_F_P1'] = df['Component2_fraction'] * df['Component2_Property1']
        df_features['C2_F2_P1'] = (df['Component2_fraction'] ** 2) * df['Component2_Property1']
    
    # Component 5 with Property 2 (most predictive)
    if 'Component5_fraction' in df.columns and 'Component5_Property2' in df.columns:
        df_features['C5_F_P2'] = df['Component5_fraction'] * df['Component5_Property2']
        df_features['C5_F2_P2'] = (df['Component5_fraction'] ** 2) * df['Component5_Property2']
    
    # 7. Blend characteristics (ESSENTIAL)
    df_features['Total_Blend_Volume'] = df[fraction_cols].sum(axis=1)
    df_features['Volume_Deviation'] = abs(df_features['Total_Blend_Volume'] - 1.0)
    
    print(f"   - Original features: {df.shape[1]}")
    print(f"   - Winner features: {df_features.shape[1]}")
    print(f"   - New features added: {df_features.shape[1] - df.shape[1]}")
    
    return df_features

def create_winner_model():
    """Create WINNER model with optimal hyperparameters"""
    print("✔️ Creating WINNER model...")
    
    # WINNER hyperparameters - optimized for maximum performance
    lgb_params = {
        'objective': 'regression',
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 25,
        'learning_rate': 0.05,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'n_estimators': 600,
        'max_depth': 7,
        'min_child_samples': 15,
        'reg_alpha': 0.02,
        'reg_lambda': 0.02,
        'min_split_gain': 0.002
    }
    
    return lgb.LGBMRegressor(**lgb_params)

def calculate_mape_score(y_true, y_pred, reference_cost):
    """Calculate leaderboard score using MAPE and reference cost"""
    mape = mean_absolute_percentage_error(y_true, y_pred)
    score = max(10, 100 - (90 * mape / reference_cost))
    return score, mape

def main():
    """Main execution function to WIN this competition ASAP"""
    print("🚀 Shell.ai Hackathon 2025: WINNER Performance - Let's ACE This!")
    print("=" * 70)
    
    # Load data
    train_df, test_df, sample_solution = load_data()
    
    # Identify feature columns
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    property_cols = [col for col in train_df.columns if 'Property' in col and 'Blend' not in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    print(f"✔️ Data overview:")
    print(f"   - Component fractions: {len(fraction_cols)}")
    print(f"   - Component properties: {len(property_cols)}")
    print(f"   - Blend properties (targets): {len(target_cols)}")
    
    # WINNER Feature Engineering
    train_features = winner_feature_engineering(train_df, fraction_cols, property_cols, is_train=True)
    test_features = winner_feature_engineering(test_df, fraction_cols, property_cols, is_train=False)
    
    # Prepare features and targets
    print("✔️ Preparing features and targets...")
    
    # Remove target columns from features
    feature_columns = [col for col in train_features.columns if col not in target_cols]
    X = train_features[feature_columns]
    y = train_features[target_cols]
    
    # Ensure test features have same columns as training features
    X_test = test_features[feature_columns]
    
    print(f"   - Feature matrix shape: {X.shape}")
    print(f"   - Target matrix shape: {y.shape}")
    print(f"   - Test matrix shape: {X_test.shape}")
    
    # Handle missing values and infinite values
    print("✔️ Cleaning data...")
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    X_test = X_test.replace([np.inf, -np.inf], np.nan)
    X_test = X_test.fillna(X.median())
    
    # Split training data
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    print(f"   - Training set: {X_train.shape[0]} samples")
    print(f"   - Validation set: {X_val.shape[0]} samples")
    
    # Create and train WINNER model
    lgb_model = create_winner_model()
    model = MultiOutputRegressor(lgb_model, n_jobs=2)
    
    print("✔️ Training WINNER model...")
    model.fit(X_train, y_train)
    
    # Make predictions on validation set
    print("✔️ Evaluating WINNER performance...")
    y_val_pred = model.predict(X_val)
    
    # Calculate individual target performance
    target_mapes = []
    for i in range(y_val.shape[1]):
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_val_pred[:, i])
        target_mapes.append(mape)
    
    avg_mape = np.mean(target_mapes)
    print(f"   - Average validation MAPE: {avg_mape:.4f}")
    
    # Calculate leaderboard scores
    public_score, public_mape = calculate_mape_score(y_val.values, y_val_pred, 2.72)
    private_score, private_mape = calculate_mape_score(y_val.values, y_val_pred, 2.58)
    
    print(f"   - Public leaderboard score: {public_score:.2f} (MAPE: {public_mape:.4f})")
    print(f"   - Private leaderboard score: {private_score:.2f} (MAPE: {private_mape:.4f})")
    
    # Make predictions on test set
    print("✔️ Making predictions on test set...")
    test_predictions = model.predict(X_test)
    
    # Create submission file
    print("✔️ Creating WINNER submission file...")
    submission_df = pd.DataFrame()
    submission_df['ID'] = test_df['ID']
    
    for i, target_col in enumerate(target_cols):
        submission_df[target_col] = test_predictions[:, i]
    
    # Save submission
    submission_df.to_csv('submission_winner.csv', index=False)
    print(f"   - WINNER submission saved as 'submission_winner.csv'")
    print(f"   - Submission shape: {submission_df.shape}")
    
    # Final summary
    print("\n" + "=" * 70)
    print("🎯 WINNER PERFORMANCE RESULTS SUMMARY")
    print("=" * 70)
    print(f"✅ WINNER model trained with {len(feature_columns)} features")
    print(f"✅ Validation MAPE: {avg_mape:.4f}")
    print(f"✅ Public score: {public_score:.2f} | Private score: {private_score:.2f}")
    print(f"✅ WINNER submission created with {len(submission_df)} predictions")
    
    # Display individual target performance
    print("\n📊 Individual Target Performance (Validation MAPE):")
    for i, (target, mape) in enumerate(zip(target_cols, target_mapes), 1):
        if mape < 0.5:
            status = "🔥 PERFECT"
        elif mape < 1.0:
            status = "⚡ EXCELLENT"
        elif mape < 1.5:
            status = "📈 GREAT"
        else:
            status = "🎯 FOCUS"
        print(f"   {i:2d}. {target:<20} {mape:.4f} {status}")
    
    # Performance analysis
    current_score = public_score
    
    print(f"\n💡 WINNER Analysis:")
    print(f"   🚀 WINNER score: {current_score:.1f}%")
    print(f"   📈 Improvement from 78%: {current_score - 78.0:+.1f} points")
    
    if current_score >= 85:
        print(f"\n🏆 🎉 WINNER! 85%+ ACHIEVED! COMPETITION ACED! 🎉 🏆")
        print(f"🌟 CONGRATULATIONS! You've ACED this competition! 🌟")
    elif current_score >= 80:
        print(f"\n⚡ EXCELLENT! Very strong performance - almost there!")
    elif current_score >= 75:
        print(f"\n📈 GREAT! Solid improvement - keep pushing!")
    else:
        print(f"\n🔧 Good foundation - consider ensemble with best previous results!")
    
    print(f"\n🏆 WINNER APPROACH for Shell.ai Hackathon!")
    
    return model, submission_df

if __name__ == "__main__":
    model, submission_df = main()
