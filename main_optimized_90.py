#!/usr/bin/env python3
"""
Shell.ai Hackathon 2025: OPTIMIZED 90%+ TARGET Fuel Blend Properties Prediction
Single-model approach with maximum optimization targeting 90%+ from current 78%

Key Optimizations:
- Highly optimized LightGBM with perfect hyperparameters
- Strategic feature engineering (most effective features)
- Advanced validation and early stopping
- Target-specific optimization
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_percentage_error
from sklearn.feature_selection import SelectKBest, f_regression
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load the training, test, and sample solution data"""
    print("✔️ Loading data files...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_solution = pd.read_csv('sample_solution.csv')
    
    print(f"   - Training data shape: {train_df.shape}")
    print(f"   - Test data shape: {test_df.shape}")
    print(f"   - Sample solution shape: {sample_solution.shape}")
    
    return train_df, test_df, sample_solution

def strategic_feature_engineering(df, fraction_cols, property_cols, is_train=True):
    """Strategic feature engineering targeting 90%+ with proven techniques"""
    print("✔️ Applying STRATEGIC feature engineering for 90%+ target...")
    
    df_features = df.copy()
    
    # 1. Multi-level weighted averages (most effective feature type)
    print("   - Creating strategic weighted features...")
    for prop_num in range(1, 11):
        weighted_sum = 0
        squared_weighted_sum = 0
        cubic_weighted_sum = 0
        
        for comp_num in range(1, 6):
            fraction_col = f'Component{comp_num}_fraction'
            property_col = f'Component{comp_num}_Property{prop_num}'
            
            if fraction_col in df.columns and property_col in df.columns:
                weighted_sum += df[fraction_col] * df[property_col]
                squared_weighted_sum += (df[fraction_col] ** 2) * df[property_col]
                cubic_weighted_sum += (df[fraction_col] ** 3) * df[property_col]
        
        df_features[f'WeightedAvg_Property{prop_num}'] = weighted_sum
        df_features[f'QuadWeightedAvg_Property{prop_num}'] = squared_weighted_sum
        df_features[f'CubicWeightedAvg_Property{prop_num}'] = cubic_weighted_sum
    
    # 2. Advanced component statistics
    df_features['Fraction_Mean'] = df[fraction_cols].mean(axis=1)
    df_features['Fraction_Std'] = df[fraction_cols].std(axis=1)
    df_features['Fraction_Variance'] = df[fraction_cols].var(axis=1)
    df_features['Fraction_Range'] = df[fraction_cols].max(axis=1) - df[fraction_cols].min(axis=1)
    df_features['Fraction_Skewness'] = df[fraction_cols].skew(axis=1)
    
    # 3. Enhanced dominant component analysis
    df_features['Dominant_Component'] = df[fraction_cols].idxmax(axis=1).str.extract(r'(\d+)').astype(int)
    df_features['Max_Fraction'] = df[fraction_cols].max(axis=1)
    df_features['Second_Max_Fraction'] = df[fraction_cols].apply(lambda x: x.nlargest(2).iloc[1], axis=1)
    df_features['Dominance_Ratio'] = df_features['Max_Fraction'] / (df_features['Second_Max_Fraction'] + 1e-10)
    
    # 4. Component diversity measures
    fractions_array = df[fraction_cols].values + 1e-10
    df_features['Shannon_Entropy'] = -np.sum(fractions_array * np.log(fractions_array), axis=1)
    df_features['Effective_Components'] = 1 / np.sum(fractions_array ** 2, axis=1)
    df_features['Simpson_Index'] = 1 - np.sum(fractions_array ** 2, axis=1)
    
    # 5. Property statistics for key properties
    key_properties = [1, 2, 3, 5, 7, 8, 10]  # Most predictive
    
    for prop_num in key_properties:
        prop_cols = [f'Component{i}_Property{prop_num}' for i in range(1, 6)]
        available_cols = [col for col in prop_cols if col in df.columns]
        
        if available_cols:
            df_features[f'Property{prop_num}_Mean'] = df[available_cols].mean(axis=1)
            df_features[f'Property{prop_num}_Std'] = df[available_cols].std(axis=1)
            df_features[f'Property{prop_num}_Range'] = df[available_cols].max(axis=1) - df[available_cols].min(axis=1)
            df_features[f'Property{prop_num}_Median'] = df[available_cols].median(axis=1)
    
    # 6. Strategic interactions (highest impact combinations)
    strategic_interactions = [
        (3, 7),  # Highest correlation: 0.997
        (1, 4),  # High correlation: 0.743
        (1, 2),  # High correlation: 0.726
        (2, 4),  # High correlation: 0.706
        (6, 9),  # Important for specific targets
        (1, 8),  # Important for BlendProperty1
        (2, 8),  # Important for BlendProperty8
        (4, 8),  # Important for BlendProperty8
    ]
    
    for prop1, prop2 in strategic_interactions:
        # Multiple interaction types
        df_features[f'WA_P{prop1}_P{prop2}_Product'] = (
            df_features[f'WeightedAvg_Property{prop1}'] * df_features[f'WeightedAvg_Property{prop2}']
        )
        df_features[f'WA_P{prop1}_P{prop2}_Ratio'] = (
            df_features[f'WeightedAvg_Property{prop1}'] / (df_features[f'WeightedAvg_Property{prop2}'] + 1e-10)
        )
        df_features[f'QWA_P{prop1}_P{prop2}_Product'] = (
            df_features[f'QuadWeightedAvg_Property{prop1}'] * df_features[f'QuadWeightedAvg_Property{prop2}']
        )
    
    # 7. Component-specific strategic interactions
    strategic_components = [(2, [1, 2, 5]), (5, [1, 2, 7]), (4, [1, 8])]  # (component, key_properties)
    
    for comp, props in strategic_components:
        fraction_col = f'Component{comp}_fraction'
        if fraction_col in df.columns:
            for prop in props:
                property_col = f'Component{comp}_Property{prop}'
                if property_col in df.columns:
                    df_features[f'C{comp}_F_P{prop}'] = df[fraction_col] * df[property_col]
                    df_features[f'C{comp}_F2_P{prop}'] = (df[fraction_col] ** 2) * df[property_col]
                    df_features[f'C{comp}_F_P{prop}_Norm'] = (
                        df[fraction_col] * df[property_col] / (df_features[f'Property{prop}_Mean'] + 1e-10)
                    )
    
    # 8. Advanced blend characteristics
    df_features['Total_Blend_Volume'] = df[fraction_cols].sum(axis=1)
    df_features['Volume_Deviation'] = abs(df_features['Total_Blend_Volume'] - 1.0)
    df_features['Mixing_Complexity'] = df_features['Shannon_Entropy'] / np.log(len(fraction_cols))
    df_features['Blend_Homogeneity'] = 1 - df_features['Fraction_Variance']
    
    # 9. Component concentration levels (strategic components only)
    for comp in [2, 5]:  # Most important components
        fraction_col = f'Component{comp}_fraction'
        if fraction_col in df.columns:
            df_features[f'Component{comp}_High_Conc'] = (df[fraction_col] > df[fraction_col].quantile(0.75)).astype(int)
            df_features[f'Component{comp}_Low_Conc'] = (df[fraction_col] < df[fraction_col].quantile(0.25)).astype(int)
            df_features[f'Component{comp}_VeryHigh_Conc'] = (df[fraction_col] > df[fraction_col].quantile(0.9)).astype(int)
    
    # 10. Cross-component ratios (most predictive pairs)
    important_pairs = [(2, 5), (1, 4), (3, 7)]
    for comp1, comp2 in important_pairs:
        frac1_col = f'Component{comp1}_fraction'
        frac2_col = f'Component{comp2}_fraction'
        if frac1_col in df.columns and frac2_col in df.columns:
            df_features[f'C{comp1}_C{comp2}_Ratio'] = df[frac1_col] / (df[frac2_col] + 1e-10)
            df_features[f'C{comp1}_C{comp2}_Product'] = df[frac1_col] * df[frac2_col]
    
    print(f"   - Original features: {df.shape[1]}")
    print(f"   - Strategic features: {df_features.shape[1]}")
    print(f"   - New features added: {df_features.shape[1] - df.shape[1]}")
    
    return df_features

def create_optimized_lgb_model():
    """Create highly optimized LightGBM model targeting 90%+"""
    print("✔️ Creating OPTIMIZED LightGBM for 90%+ target...")
    
    # Highly optimized parameters for maximum performance
    lgb_params = {
        'objective': 'regression',
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 31,  # Optimal for this dataset size
        'learning_rate': 0.02,  # Lower for better convergence
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'n_estimators': 1200,  # More estimators for better performance
        'max_depth': 8,  # Deeper trees for complex patterns
        'min_child_samples': 10,  # Lower for more detailed learning
        'reg_alpha': 0.005,  # Minimal regularization for max performance
        'reg_lambda': 0.005,
        'min_split_gain': 0.0005,  # Lower threshold for more splits
        'subsample_for_bin': 200000,
        'force_col_wise': True,
        'extra_trees': False,
        'max_bin': 255,
        'min_data_in_bin': 3
    }
    
    return lgb.LGBMRegressor(**lgb_params)

def calculate_mape_score(y_true, y_pred, reference_cost):
    """Calculate leaderboard score using MAPE and reference cost"""
    mape = mean_absolute_percentage_error(y_true, y_pred)
    score = max(10, 100 - (90 * mape / reference_cost))
    return score, mape

def main():
    """Main execution function targeting 90%+ performance with optimized approach"""
    print("🚀 Shell.ai Hackathon 2025: OPTIMIZED 90%+ TARGET Fuel Blend Properties Prediction")
    print("=" * 90)
    
    # Load data
    train_df, test_df, sample_solution = load_data()
    
    # Identify feature columns
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    property_cols = [col for col in train_df.columns if 'Property' in col and 'Blend' not in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    print(f"✔️ Data overview:")
    print(f"   - Component fractions: {len(fraction_cols)}")
    print(f"   - Component properties: {len(property_cols)}")
    print(f"   - Blend properties (targets): {len(target_cols)}")
    
    # Strategic Feature Engineering
    train_features = strategic_feature_engineering(train_df, fraction_cols, property_cols, is_train=True)
    test_features = strategic_feature_engineering(test_df, fraction_cols, property_cols, is_train=False)
    
    # Prepare features and targets
    print("✔️ Preparing features and targets...")
    
    # Remove target columns from features
    feature_columns = [col for col in train_features.columns if col not in target_cols]
    X = train_features[feature_columns]
    y = train_features[target_cols]
    
    # Ensure test features have same columns as training features
    X_test = test_features[feature_columns]
    
    print(f"   - Feature matrix shape: {X.shape}")
    print(f"   - Target matrix shape: {y.shape}")
    print(f"   - Test matrix shape: {X_test.shape}")
    
    # Handle missing values and infinite values
    print("✔️ Cleaning data...")
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    X_test = X_test.replace([np.inf, -np.inf], np.nan)
    X_test = X_test.fillna(X.median())
    
    # Strategic feature selection
    print("✔️ Performing strategic feature selection...")
    selector = SelectKBest(score_func=f_regression, k=min(130, X.shape[1]))
    X_selected = pd.DataFrame(
        selector.fit_transform(X, y.iloc[:, 0]),  # Use BlendProperty1 for selection
        columns=X.columns[selector.get_support()],
        index=X.index
    )
    X_test_selected = pd.DataFrame(
        selector.transform(X_test),
        columns=X.columns[selector.get_support()],
        index=X_test.index
    )
    
    print(f"   - Selected {X_selected.shape[1]} features from {X.shape[1]}")
    
    # Split training data with optimal ratio
    X_train, X_val, y_train, y_val = train_test_split(
        X_selected, y, test_size=0.12, random_state=42  # Smaller validation for more training data
    )
    
    print(f"   - Training set: {X_train.shape[0]} samples")
    print(f"   - Validation set: {X_val.shape[0]} samples")
    
    # Create and train optimized model
    lgb_model = create_optimized_lgb_model()
    model = MultiOutputRegressor(lgb_model, n_jobs=2)
    
    print("✔️ Training OPTIMIZED model for 90%+ target...")
    model.fit(X_train, y_train)
    
    # Make predictions on validation set
    print("✔️ Evaluating 90%+ target performance...")
    y_val_pred = model.predict(X_val)
    
    # Calculate individual target performance
    target_mapes = []
    for i in range(y_val.shape[1]):
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_val_pred[:, i])
        target_mapes.append(mape)
    
    avg_mape = np.mean(target_mapes)
    print(f"   - Average validation MAPE: {avg_mape:.4f}")
    
    # Calculate leaderboard scores
    public_score, public_mape = calculate_mape_score(y_val.values, y_val_pred, 2.72)
    private_score, private_mape = calculate_mape_score(y_val.values, y_val_pred, 2.58)
    
    print(f"   - Public leaderboard score: {public_score:.2f} (MAPE: {public_mape:.4f})")
    print(f"   - Private leaderboard score: {private_score:.2f} (MAPE: {private_mape:.4f})")
    
    # Make predictions on test set
    print("✔️ Making predictions on test set...")
    test_predictions = model.predict(X_test_selected)
    
    # Create submission file
    print("✔️ Creating OPTIMIZED 90%+ target submission file...")
    submission_df = pd.DataFrame()
    submission_df['ID'] = test_df['ID']
    
    for i, target_col in enumerate(target_cols):
        submission_df[target_col] = test_predictions[:, i]
    
    # Save submission
    submission_df.to_csv('submission_optimized_90.csv', index=False)
    print(f"   - OPTIMIZED 90%+ submission saved as 'submission_optimized_90.csv'")
    print(f"   - Submission shape: {submission_df.shape}")
    
    # Final summary
    print("\n" + "=" * 90)
    print("🎯 OPTIMIZED 90%+ TARGET RESULTS SUMMARY")
    print("=" * 90)
    print(f"✅ OPTIMIZED model trained with {X_selected.shape[1]} features")
    print(f"✅ Validation MAPE: {avg_mape:.4f}")
    print(f"✅ Public score: {public_score:.2f} | Private score: {private_score:.2f}")
    print(f"✅ OPTIMIZED 90%+ submission created with {len(submission_df)} predictions")
    
    # Display individual target performance
    print("\n📊 Individual Target Performance (Validation MAPE):")
    target_performance = list(zip(target_cols, target_mapes))
    target_performance.sort(key=lambda x: x[1])
    
    for i, (target, mape) in enumerate(target_performance, 1):
        if mape < 0.25:
            status = "🔥 PERFECT"
        elif mape < 0.4:
            status = "⚡ EXCELLENT"
        elif mape < 0.6:
            status = "📈 GREAT"
        elif mape < 1.0:
            status = "🎯 GOOD"
        else:
            status = "🔧 FOCUS"
        
        print(f"   {i:2d}. {target:<20} {mape:.4f} {status}")
    
    # Performance analysis
    current_score = public_score
    target_score = 90.0
    improvement_from_78 = current_score - 78.0
    
    print(f"\n💡 90%+ Target Analysis:")
    print(f"   📊 Current score: {current_score:.1f}%")
    print(f"   🏆 Target score: {target_score:.1f}%")
    print(f"   📈 Improvement from 78%: {improvement_from_78:+.1f} points")
    print(f"   🎯 Gap to 90%: {target_score - current_score:.1f} points")
    
    if current_score >= 90:
        print(f"\n🏆 🎉 90%+ TARGET ACHIEVED! OUTSTANDING PERFORMANCE! 🎉 🏆")
        print(f"🌟 CONGRATULATIONS! You've reached the 90%+ performance goal! 🌟")
    elif current_score >= 87:
        print(f"\n⚡ EXCELLENT! Almost at 90%+ target - outstanding optimization!")
    elif current_score >= 83:
        print(f"\n📈 GREAT PROGRESS! Significant improvement towards 90%+ target!")
    elif current_score >= 80:
        print(f"\n🔧 GOOD PROGRESS! Solid improvement from 78% baseline!")
    else:
        print(f"\n🔧 FOUNDATION SET! Continue optimizing for 90%+ target!")
    
    print(f"\n🏆 MAXIMUM OPTIMIZATION for Shell.ai Hackathon - Targeting 90%+!")
    
    return model, submission_df

if __name__ == "__main__":
    model, submission_df = main()
