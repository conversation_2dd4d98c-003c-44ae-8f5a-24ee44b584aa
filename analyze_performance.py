#!/usr/bin/env python3
"""
Deep Performance Analysis for Shell.ai Hackathon Solution
Identify weaknesses and optimization opportunities
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_percentage_error, mean_squared_error, r2_score
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

def analyze_target_correlations():
    """Analyze correlations between targets and identify patterns"""
    print("🔍 Analyzing target correlations...")
    
    train_df = pd.read_csv('train.csv')
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    # Correlation matrix
    corr_matrix = train_df[target_cols].corr()
    
    plt.figure(figsize=(12, 10))
    sns.heatmap(corr_matrix, annot=True, cmap='RdBu_r', center=0, 
                square=True, fmt='.3f', cbar_kws={'shrink': 0.8})
    plt.title('Target Variables Correlation Matrix')
    plt.tight_layout()
    plt.savefig('target_correlations.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Find highly correlated pairs
    high_corr_pairs = []
    for i in range(len(target_cols)):
        for j in range(i+1, len(target_cols)):
            corr_val = corr_matrix.iloc[i, j]
            if abs(corr_val) > 0.5:
                high_corr_pairs.append((target_cols[i], target_cols[j], corr_val))
    
    print(f"\n🔗 Highly correlated target pairs (|r| > 0.5):")
    for target1, target2, corr in sorted(high_corr_pairs, key=lambda x: abs(x[2]), reverse=True):
        print(f"   {target1} ↔ {target2}: {corr:.3f}")
    
    return corr_matrix, high_corr_pairs

def analyze_feature_target_relationships():
    """Analyze relationships between features and targets"""
    print("\n🎯 Analyzing feature-target relationships...")
    
    train_df = pd.read_csv('train.csv')
    
    # Component fractions
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    # Calculate correlations between fractions and targets
    fraction_target_corr = {}
    for target in target_cols:
        correlations = []
        for fraction in fraction_cols:
            corr = train_df[fraction].corr(train_df[target])
            correlations.append((fraction, corr))
        fraction_target_corr[target] = sorted(correlations, key=lambda x: abs(x[1]), reverse=True)
    
    # Display strongest correlations
    print("\n📊 Strongest fraction-target correlations:")
    for target in target_cols:
        top_corr = fraction_target_corr[target][0]
        print(f"   {target}: {top_corr[0]} ({top_corr[1]:.3f})")
    
    return fraction_target_corr

def analyze_residuals():
    """Analyze prediction residuals to identify patterns"""
    print("\n📈 Analyzing prediction residuals...")
    
    # Load data and recreate model quickly
    train_df = pd.read_csv('train.csv')
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    feature_cols = [col for col in train_df.columns if col not in target_cols]
    
    X = train_df[feature_cols]
    y = train_df[target_cols]
    
    X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Quick model training
    model = MultiOutputRegressor(lgb.LGBMRegressor(n_estimators=100, random_state=42, verbose=-1))
    model.fit(X_train, y_train)
    
    # Predictions
    y_pred = model.predict(X_val)
    
    # Calculate residuals for each target
    residuals = {}
    metrics = {}
    
    for i, target in enumerate(target_cols):
        residual = y_val.iloc[:, i] - y_pred[:, i]
        residuals[target] = residual
        
        # Calculate metrics
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_pred[:, i])
        rmse = np.sqrt(mean_squared_error(y_val.iloc[:, i], y_pred[:, i]))
        r2 = r2_score(y_val.iloc[:, i], y_pred[:, i])
        
        metrics[target] = {'MAPE': mape, 'RMSE': rmse, 'R2': r2}
    
    # Display metrics
    print("\n📊 Individual Target Metrics:")
    print("-" * 60)
    print(f"{'Target':<20} {'MAPE':<10} {'RMSE':<10} {'R²':<10}")
    print("-" * 60)
    
    for target in target_cols:
        m = metrics[target]
        print(f"{target:<20} {m['MAPE']:<10.4f} {m['RMSE']:<10.4f} {m['R2']:<10.4f}")
    
    # Identify worst performing targets
    worst_targets = sorted(metrics.items(), key=lambda x: x[1]['MAPE'], reverse=True)[:3]
    print(f"\n⚠️  Worst performing targets (highest MAPE):")
    for target, m in worst_targets:
        print(f"   {target}: MAPE = {m['MAPE']:.4f}")
    
    return residuals, metrics

def analyze_data_distribution():
    """Analyze data distributions and identify outliers"""
    print("\n📊 Analyzing data distributions...")
    
    train_df = pd.read_csv('train.csv')
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    # Check for outliers in targets
    outlier_info = {}
    
    for target in target_cols:
        Q1 = train_df[target].quantile(0.25)
        Q3 = train_df[target].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        outliers = train_df[(train_df[target] < lower_bound) | (train_df[target] > upper_bound)]
        outlier_info[target] = {
            'count': len(outliers),
            'percentage': len(outliers) / len(train_df) * 100,
            'bounds': (lower_bound, upper_bound)
        }
    
    print("\n🎯 Outlier Analysis:")
    print("-" * 50)
    print(f"{'Target':<20} {'Outliers':<10} {'Percentage':<12}")
    print("-" * 50)
    
    for target in target_cols:
        info = outlier_info[target]
        print(f"{target:<20} {info['count']:<10} {info['percentage']:<12.2f}%")
    
    return outlier_info

def identify_improvement_opportunities():
    """Identify specific areas for improvement"""
    print("\n🚀 Improvement Opportunities Identified:")
    print("=" * 60)
    
    opportunities = [
        "1. 🎯 Target-Specific Models: Train separate models for worst-performing targets",
        "2. 🔄 Ensemble Methods: Combine multiple algorithms (XGBoost, CatBoost, Neural Networks)",
        "3. 🧮 Advanced Feature Engineering: Polynomial features, PCA, domain-specific transforms",
        "4. 🎛️  Hyperparameter Optimization: Bayesian optimization for each target",
        "5. 📊 Outlier Handling: Robust scaling and outlier treatment",
        "6. 🔗 Multi-target Learning: Leverage target correlations with chain regressors",
        "7. 📈 Feature Selection: Remove noise and select optimal feature subsets",
        "8. 🎨 Data Augmentation: Generate synthetic samples for better generalization",
        "9. 🔍 Cross-Validation: Implement stratified K-fold for robust evaluation",
        "10. 🎪 Stacking: Multi-level ensemble with meta-learners"
    ]
    
    for opportunity in opportunities:
        print(f"   {opportunity}")
    
    print("\n💡 Priority Actions:")
    print("   → Focus on BlendProperty8 (worst MAPE)")
    print("   → Implement ensemble of 3-5 different algorithms")
    print("   → Add polynomial and interaction features")
    print("   → Use Bayesian hyperparameter optimization")
    print("   → Implement proper cross-validation")

def main():
    """Main analysis function"""
    print("🔬 Deep Performance Analysis for Shell.ai Hackathon")
    print("=" * 60)
    
    # Run all analyses
    corr_matrix, high_corr_pairs = analyze_target_correlations()
    fraction_target_corr = analyze_feature_target_relationships()
    residuals, metrics = analyze_residuals()
    outlier_info = analyze_data_distribution()
    identify_improvement_opportunities()
    
    print("\n✅ Analysis complete! Check generated visualizations.")
    
    return {
        'correlations': corr_matrix,
        'high_corr_pairs': high_corr_pairs,
        'fraction_target_corr': fraction_target_corr,
        'residuals': residuals,
        'metrics': metrics,
        'outliers': outlier_info
    }

if __name__ == "__main__":
    analysis_results = main()
