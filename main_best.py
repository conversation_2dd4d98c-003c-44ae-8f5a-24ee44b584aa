#!/usr/bin/env python3
"""
Shell.ai Hackathon 2025: BEST Performance Fuel Blend Properties Prediction
Streamlined approach targeting maximum performance from current 78%

Key Optimizations:
- Proven effective feature engineering
- Optimized LightGBM with best hyperparameters
- Efficient training approach
- Focus on most impactful improvements
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_percentage_error
from sklearn.feature_selection import SelectKBest, f_regression
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load the training, test, and sample solution data"""
    print("✔️ Loading data files...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_solution = pd.read_csv('sample_solution.csv')
    
    print(f"   - Training data shape: {train_df.shape}")
    print(f"   - Test data shape: {test_df.shape}")
    print(f"   - Sample solution shape: {sample_solution.shape}")
    
    return train_df, test_df, sample_solution

def best_feature_engineering(df, fraction_cols, property_cols, is_train=True):
    """Best feature engineering with proven effective techniques"""
    print("✔️ Applying BEST feature engineering...")
    
    df_features = df.copy()
    
    # 1. Multi-level weighted averages (most effective)
    print("   - Creating best weighted features...")
    for prop_num in range(1, 11):
        weighted_sum = 0
        squared_weighted_sum = 0
        cubic_weighted_sum = 0
        
        for comp_num in range(1, 6):
            fraction_col = f'Component{comp_num}_fraction'
            property_col = f'Component{comp_num}_Property{prop_num}'
            
            if fraction_col in df.columns and property_col in df.columns:
                weighted_sum += df[fraction_col] * df[property_col]
                squared_weighted_sum += (df[fraction_col] ** 2) * df[property_col]
                cubic_weighted_sum += (df[fraction_col] ** 3) * df[property_col]
        
        df_features[f'WeightedAvg_Property{prop_num}'] = weighted_sum
        df_features[f'QuadWeightedAvg_Property{prop_num}'] = squared_weighted_sum
        df_features[f'CubicWeightedAvg_Property{prop_num}'] = cubic_weighted_sum
    
    # 2. Component statistics
    df_features['Fraction_Variance'] = df[fraction_cols].var(axis=1)
    df_features['Fraction_Std'] = df[fraction_cols].std(axis=1)
    df_features['Fraction_Range'] = df[fraction_cols].max(axis=1) - df[fraction_cols].min(axis=1)
    df_features['Fraction_Skewness'] = df[fraction_cols].skew(axis=1)
    
    # 3. Dominant component analysis
    df_features['Dominant_Component'] = df[fraction_cols].idxmax(axis=1).str.extract(r'(\d+)').astype(int)
    df_features['Max_Fraction'] = df[fraction_cols].max(axis=1)
    df_features['Second_Max_Fraction'] = df[fraction_cols].apply(lambda x: x.nlargest(2).iloc[1], axis=1)
    df_features['Dominance_Ratio'] = df_features['Max_Fraction'] / (df_features['Second_Max_Fraction'] + 1e-10)
    
    # 4. Component diversity
    fractions_array = df[fraction_cols].values + 1e-10
    df_features['Shannon_Entropy'] = -np.sum(fractions_array * np.log(fractions_array), axis=1)
    df_features['Effective_Components'] = 1 / np.sum(fractions_array ** 2, axis=1)
    
    # 5. Property statistics for key properties
    key_properties = [1, 2, 3, 5, 7, 8, 10]
    
    for prop_num in key_properties:
        prop_cols = [f'Component{i}_Property{prop_num}' for i in range(1, 6)]
        available_cols = [col for col in prop_cols if col in df.columns]
        
        if available_cols:
            df_features[f'Property{prop_num}_Mean'] = df[available_cols].mean(axis=1)
            df_features[f'Property{prop_num}_Std'] = df[available_cols].std(axis=1)
            df_features[f'Property{prop_num}_Range'] = df[available_cols].max(axis=1) - df[available_cols].min(axis=1)
    
    # 6. Key interactions (highest impact)
    key_interactions = [
        (3, 7), (1, 4), (1, 2), (2, 4), (6, 9), (1, 8), (2, 8), (4, 8)
    ]
    
    for prop1, prop2 in key_interactions:
        df_features[f'WA_P{prop1}_P{prop2}_Product'] = (
            df_features[f'WeightedAvg_Property{prop1}'] * df_features[f'WeightedAvg_Property{prop2}']
        )
        df_features[f'WA_P{prop1}_P{prop2}_Ratio'] = (
            df_features[f'WeightedAvg_Property{prop1}'] / (df_features[f'WeightedAvg_Property{prop2}'] + 1e-10)
        )
    
    # 7. Component-specific interactions (most important)
    important_components = [2, 5]
    important_properties = [1, 2, 5, 7]
    
    for comp in important_components:
        fraction_col = f'Component{comp}_fraction'
        if fraction_col in df.columns:
            for prop in important_properties:
                property_col = f'Component{comp}_Property{prop}'
                if property_col in df.columns:
                    df_features[f'C{comp}_F_P{prop}'] = df[fraction_col] * df[property_col]
                    df_features[f'C{comp}_F2_P{prop}'] = (df[fraction_col] ** 2) * df[property_col]
    
    # 8. Blend characteristics
    df_features['Total_Blend_Volume'] = df[fraction_cols].sum(axis=1)
    df_features['Volume_Deviation'] = abs(df_features['Total_Blend_Volume'] - 1.0)
    df_features['Mixing_Complexity'] = df_features['Shannon_Entropy'] / np.log(len(fraction_cols))
    
    print(f"   - Original features: {df.shape[1]}")
    print(f"   - Best features: {df_features.shape[1]}")
    print(f"   - New features added: {df_features.shape[1] - df.shape[1]}")
    
    return df_features

def create_best_model():
    """Create best performing LightGBM model"""
    print("✔️ Creating BEST LightGBM model...")
    
    # Best parameters for maximum performance
    lgb_params = {
        'objective': 'regression',
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 25,
        'learning_rate': 0.025,  # Optimal learning rate
        'feature_fraction': 0.85,
        'bagging_fraction': 0.85,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'n_estimators': 800,  # Optimal number of estimators
        'max_depth': 7,
        'min_child_samples': 12,
        'reg_alpha': 0.01,
        'reg_lambda': 0.01,
        'min_split_gain': 0.001,
        'subsample_for_bin': 200000,
        'force_col_wise': True
    }
    
    return lgb.LGBMRegressor(**lgb_params)

def calculate_mape_score(y_true, y_pred, reference_cost):
    """Calculate leaderboard score using MAPE and reference cost"""
    mape = mean_absolute_percentage_error(y_true, y_pred)
    score = max(10, 100 - (90 * mape / reference_cost))
    return score, mape

def main():
    """Main execution function for best performance"""
    print("🚀 Shell.ai Hackathon 2025: BEST Performance Fuel Blend Properties Prediction")
    print("=" * 80)
    
    # Load data
    train_df, test_df, sample_solution = load_data()
    
    # Identify feature columns
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    property_cols = [col for col in train_df.columns if 'Property' in col and 'Blend' not in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    print(f"✔️ Data overview:")
    print(f"   - Component fractions: {len(fraction_cols)}")
    print(f"   - Component properties: {len(property_cols)}")
    print(f"   - Blend properties (targets): {len(target_cols)}")
    
    # Best Feature Engineering
    train_features = best_feature_engineering(train_df, fraction_cols, property_cols, is_train=True)
    test_features = best_feature_engineering(test_df, fraction_cols, property_cols, is_train=False)
    
    # Prepare features and targets
    print("✔️ Preparing features and targets...")
    
    # Remove target columns from features
    feature_columns = [col for col in train_features.columns if col not in target_cols]
    X = train_features[feature_columns]
    y = train_features[target_cols]
    
    # Ensure test features have same columns as training features
    X_test = test_features[feature_columns]
    
    print(f"   - Feature matrix shape: {X.shape}")
    print(f"   - Target matrix shape: {y.shape}")
    print(f"   - Test matrix shape: {X_test.shape}")
    
    # Handle missing values and infinite values
    print("✔️ Cleaning data...")
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    X_test = X_test.replace([np.inf, -np.inf], np.nan)
    X_test = X_test.fillna(X.median())
    
    # Feature selection
    print("✔️ Performing feature selection...")
    selector = SelectKBest(score_func=f_regression, k=min(100, X.shape[1]))
    X_selected = pd.DataFrame(
        selector.fit_transform(X, y.iloc[:, 0]),
        columns=X.columns[selector.get_support()],
        index=X.index
    )
    X_test_selected = pd.DataFrame(
        selector.transform(X_test),
        columns=X.columns[selector.get_support()],
        index=X_test.index
    )
    
    print(f"   - Selected {X_selected.shape[1]} features from {X.shape[1]}")
    
    # Split training data
    X_train, X_val, y_train, y_val = train_test_split(
        X_selected, y, test_size=0.15, random_state=42
    )
    
    print(f"   - Training set: {X_train.shape[0]} samples")
    print(f"   - Validation set: {X_val.shape[0]} samples")
    
    # Create and train best model
    lgb_model = create_best_model()
    model = MultiOutputRegressor(lgb_model, n_jobs=2)
    
    print("✔️ Training BEST model...")
    model.fit(X_train, y_train)
    
    # Make predictions on validation set
    print("✔️ Evaluating BEST performance...")
    y_val_pred = model.predict(X_val)
    
    # Calculate individual target performance
    target_mapes = []
    for i in range(y_val.shape[1]):
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_val_pred[:, i])
        target_mapes.append(mape)
    
    avg_mape = np.mean(target_mapes)
    print(f"   - Average validation MAPE: {avg_mape:.4f}")
    
    # Calculate leaderboard scores
    public_score, public_mape = calculate_mape_score(y_val.values, y_val_pred, 2.72)
    private_score, private_mape = calculate_mape_score(y_val.values, y_val_pred, 2.58)
    
    print(f"   - Public leaderboard score: {public_score:.2f} (MAPE: {public_mape:.4f})")
    print(f"   - Private leaderboard score: {private_score:.2f} (MAPE: {private_mape:.4f})")
    
    # Make predictions on test set
    print("✔️ Making predictions on test set...")
    test_predictions = model.predict(X_test_selected)
    
    # Create submission file
    print("✔️ Creating BEST submission file...")
    submission_df = pd.DataFrame()
    submission_df['ID'] = test_df['ID']
    
    for i, target_col in enumerate(target_cols):
        submission_df[target_col] = test_predictions[:, i]
    
    # Save submission
    submission_df.to_csv('submission_best.csv', index=False)
    print(f"   - BEST submission saved as 'submission_best.csv'")
    print(f"   - Submission shape: {submission_df.shape}")
    
    # Final summary
    print("\n" + "=" * 80)
    print("🎯 BEST PERFORMANCE RESULTS SUMMARY")
    print("=" * 80)
    print(f"✅ BEST model trained with {X_selected.shape[1]} features")
    print(f"✅ Validation MAPE: {avg_mape:.4f}")
    print(f"✅ Public score: {public_score:.2f} | Private score: {private_score:.2f}")
    print(f"✅ BEST submission created with {len(submission_df)} predictions")
    
    # Display individual target performance
    print("\n📊 Individual Target Performance (Validation MAPE):")
    target_performance = list(zip(target_cols, target_mapes))
    target_performance.sort(key=lambda x: x[1])
    
    for i, (target, mape) in enumerate(target_performance, 1):
        if mape < 0.3:
            status = "🔥 PERFECT"
        elif mape < 0.5:
            status = "⚡ EXCELLENT"
        elif mape < 0.8:
            status = "📈 GREAT"
        elif mape < 1.2:
            status = "🎯 GOOD"
        else:
            status = "🔧 FOCUS"
        
        print(f"   {i:2d}. {target:<20} {mape:.4f} {status}")
    
    # Performance analysis
    current_score = public_score
    improvement_from_78 = current_score - 78.0
    
    print(f"\n💡 Performance Analysis:")
    print(f"   📊 Current score: {current_score:.1f}%")
    print(f"   📈 Improvement from 78%: {improvement_from_78:+.1f} points")
    
    if current_score >= 90:
        print(f"\n🏆 🎉 90%+ TARGET ACHIEVED! OUTSTANDING! 🎉 🏆")
    elif current_score >= 85:
        print(f"\n⚡ EXCELLENT! Very close to 90%+ - outstanding performance!")
    elif current_score >= 80:
        print(f"\n📈 GREAT! Solid improvement from 78% baseline!")
    else:
        print(f"\n🔧 GOOD! Foundation for further optimization!")
    
    print(f"\n🏆 BEST PERFORMANCE for Shell.ai Hackathon!")
    
    return model, submission_df

if __name__ == "__main__":
    model, submission_df = main()
