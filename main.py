#!/usr/bin/env python3
"""
Shell.ai Hackathon 2025: Fuel Blend Properties Prediction Challenge
Complete ML Pipeline for Multi-target Regression

Author: Augment Agent
Date: 2025-07-06
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_percentage_error
from sklearn.preprocessing import StandardScaler
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load the training, test, and sample solution data"""
    print("✔️ Loading data files...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_solution = pd.read_csv('sample_solution.csv')
    
    print(f"   - Training data shape: {train_df.shape}")
    print(f"   - Test data shape: {test_df.shape}")
    print(f"   - Sample solution shape: {sample_solution.shape}")
    
    return train_df, test_df, sample_solution

def exploratory_data_analysis(train_df):
    """Perform comprehensive EDA with visualizations"""
    print("✔️ Performing Exploratory Data Analysis...")
    
    # Basic statistics
    print("\n📊 Dataset Overview:")
    print(f"   - Training samples: {len(train_df)}")
    print(f"   - Features: {train_df.shape[1] - 10}")  # Excluding 10 target variables
    print(f"   - Target variables: 10 (BlendProperty1-10)")
    
    # Check for missing values
    missing_values = train_df.isnull().sum().sum()
    print(f"   - Missing values: {missing_values}")
    
    # Identify feature columns
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    property_cols = [col for col in train_df.columns if 'Property' in col and 'Blend' not in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    print(f"   - Component fractions: {len(fraction_cols)}")
    print(f"   - Component properties: {len(property_cols)}")
    print(f"   - Blend properties (targets): {len(target_cols)}")
    
    # Create visualizations
    plt.figure(figsize=(20, 15))
    
    # 1. Component fractions distribution
    plt.subplot(3, 3, 1)
    train_df[fraction_cols].boxplot()
    plt.title('Component Fractions Distribution')
    plt.xticks(rotation=45)
    
    # 2. Target variables distribution
    plt.subplot(3, 3, 2)
    train_df[target_cols].hist(bins=30, alpha=0.7)
    plt.title('Blend Properties Distribution')
    plt.legend(target_cols, bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 3. Correlation heatmap for blend properties
    plt.subplot(3, 3, 3)
    correlation_matrix = train_df[target_cols].corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.2f', cbar_kws={'shrink': 0.8})
    plt.title('Blend Properties Correlation')
    
    # 4. Component fractions vs first blend property
    plt.subplot(3, 3, 4)
    for col in fraction_cols:
        plt.scatter(train_df[col], train_df['BlendProperty1'], alpha=0.5, label=col)
    plt.xlabel('Component Fraction')
    plt.ylabel('BlendProperty1')
    plt.title('Component Fractions vs BlendProperty1')
    plt.legend()
    
    # 5. Average component properties
    plt.subplot(3, 3, 5)
    component_props = []
    for i in range(1, 6):  # Components 1-5
        comp_props = [col for col in property_cols if f'Component{i}_' in col]
        if comp_props:
            component_props.append(train_df[comp_props].mean().mean())
    plt.bar(range(1, 6), component_props)
    plt.xlabel('Component')
    plt.ylabel('Average Property Value')
    plt.title('Average Properties by Component')
    
    # 6. Blend property ranges
    plt.subplot(3, 3, 6)
    blend_ranges = [(train_df[col].max() - train_df[col].min()) for col in target_cols]
    plt.bar(range(1, 11), blend_ranges)
    plt.xlabel('Blend Property')
    plt.ylabel('Range (Max - Min)')
    plt.title('Blend Property Ranges')
    
    plt.tight_layout()
    plt.savefig('eda_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return fraction_cols, property_cols, target_cols

def feature_engineering(df, fraction_cols, property_cols, is_train=True):
    """Apply comprehensive feature engineering"""
    print("✔️ Applying feature engineering...")
    
    # Create a copy to avoid modifying original data
    df_features = df.copy()
    
    # 1. Weighted average of component properties using blend composition
    print("   - Creating weighted averages...")
    for prop_num in range(1, 11):  # Properties 1-10
        weighted_avg_col = f'WeightedAvg_Property{prop_num}'
        weighted_sum = 0
        
        for comp_num in range(1, 6):  # Components 1-5
            fraction_col = f'Component{comp_num}_fraction'
            property_col = f'Component{comp_num}_Property{prop_num}'
            
            if fraction_col in df.columns and property_col in df.columns:
                weighted_sum += df[fraction_col] * df[property_col]
        
        df_features[weighted_avg_col] = weighted_sum
    
    # 2. Variance of component volumes (fractions)
    print("   - Calculating component fraction variance...")
    df_features['Fraction_Variance'] = df[fraction_cols].var(axis=1)
    df_features['Fraction_Std'] = df[fraction_cols].std(axis=1)
    
    # 3. Total blend volume (should sum to 1, but check for deviations)
    print("   - Computing total blend volume...")
    df_features['Total_Blend_Volume'] = df[fraction_cols].sum(axis=1)
    df_features['Volume_Deviation'] = abs(df_features['Total_Blend_Volume'] - 1.0)
    
    # 4. Additional engineered features
    print("   - Creating additional features...")
    
    # Dominant component (component with highest fraction)
    df_features['Dominant_Component'] = df[fraction_cols].idxmax(axis=1)
    df_features['Dominant_Component'] = df_features['Dominant_Component'].str.extract(r'(\d+)').astype(int)
    df_features['Max_Fraction'] = df[fraction_cols].max(axis=1)
    df_features['Min_Fraction'] = df[fraction_cols].min(axis=1)
    
    # Component diversity (entropy-like measure)
    fractions_array = df[fraction_cols].values
    # Add small epsilon to avoid log(0)
    fractions_array = fractions_array + 1e-10
    df_features['Component_Diversity'] = -np.sum(fractions_array * np.log(fractions_array), axis=1)
    
    # Property statistics across components
    for prop_num in range(1, 11):
        prop_cols = [f'Component{i}_Property{prop_num}' for i in range(1, 6)]
        available_cols = [col for col in prop_cols if col in df.columns]
        
        if available_cols:
            df_features[f'Property{prop_num}_Mean'] = df[available_cols].mean(axis=1)
            df_features[f'Property{prop_num}_Std'] = df[available_cols].std(axis=1)
            df_features[f'Property{prop_num}_Range'] = df[available_cols].max(axis=1) - df[available_cols].min(axis=1)
    
    # Interaction features between fractions and properties (limited to reduce complexity)
    print("   - Creating interaction features...")
    for i in range(1, 4):  # Only first 3 components
        fraction_col = f'Component{i}_fraction'
        if fraction_col in df.columns:
            # Interaction with first 2 properties only
            for prop_num in range(1, 3):
                property_col = f'Component{i}_Property{prop_num}'
                if property_col in df.columns:
                    df_features[f'Interaction_C{i}_F_P{prop_num}'] = df[fraction_col] * df[property_col]
    
    print(f"   - Original features: {df.shape[1]}")
    print(f"   - Engineered features: {df_features.shape[1]}")
    print(f"   - New features added: {df_features.shape[1] - df.shape[1]}")
    
    return df_features

def calculate_mape_score(y_true, y_pred, reference_cost):
    """Calculate leaderboard score using MAPE and reference cost"""
    mape = mean_absolute_percentage_error(y_true, y_pred)
    score = max(10, 100 - (90 * mape / reference_cost))
    return score, mape

def train_model(X_train, y_train, X_val, y_val):
    """Train LightGBM model with MultiOutputRegressor"""
    print("✔️ Training LightGBM model...")
    
    # LightGBM parameters optimized for multi-target regression
    lgb_params = {
        'objective': 'regression',
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 15,
        'learning_rate': 0.1,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'n_estimators': 200,
        'max_depth': 6
    }
    
    # Create and train multi-output model
    base_model = lgb.LGBMRegressor(**lgb_params)
    model = MultiOutputRegressor(base_model, n_jobs=2)
    
    # Train the model
    model.fit(X_train, y_train)
    
    # Make predictions on validation set
    y_val_pred = model.predict(X_val)
    
    # Calculate MAPE for each target
    target_mapes = []
    for i in range(y_val.shape[1]):
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_val_pred[:, i])
        target_mapes.append(mape)
    
    avg_mape = np.mean(target_mapes)
    print(f"   - Average validation MAPE: {avg_mape:.4f}")
    
    # Calculate leaderboard scores
    public_score, public_mape = calculate_mape_score(y_val.values, y_val_pred, 2.72)
    private_score, private_mape = calculate_mape_score(y_val.values, y_val_pred, 2.58)
    
    print(f"   - Public leaderboard score: {public_score:.2f} (MAPE: {public_mape:.4f})")
    print(f"   - Private leaderboard score: {private_score:.2f} (MAPE: {private_mape:.4f})")
    
    return model, target_mapes

def extract_feature_importance(model, feature_names):
    """Extract and display feature importances"""
    print("✔️ Analyzing feature importance...")
    
    # Get feature importances from each estimator
    importances = []
    for estimator in model.estimators_:
        importances.append(estimator.feature_importances_)
    
    # Average importance across all targets
    avg_importance = np.mean(importances, axis=0)
    
    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': avg_importance
    }).sort_values('importance', ascending=False)
    
    # Display top 20 features
    print("\n🔝 Top 20 Most Important Features:")
    for i, (_, row) in enumerate(importance_df.head(20).iterrows(), 1):
        print(f"   {i:2d}. {row['feature']:<30} {row['importance']:.4f}")
    
    # Save feature importance plot
    plt.figure(figsize=(12, 8))
    top_features = importance_df.head(20)
    plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel('Feature Importance')
    plt.title('Top 20 Feature Importances (Average across all targets)')
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return importance_df

def main():
    """Main execution function"""
    print("🚀 Shell.ai Hackathon 2025: Fuel Blend Properties Prediction")
    print("=" * 60)
    
    # Load data
    train_df, test_df, sample_solution = load_data()
    
    # Exploratory Data Analysis
    fraction_cols, property_cols, target_cols = exploratory_data_analysis(train_df)
    
    # Feature Engineering
    train_features = feature_engineering(train_df, fraction_cols, property_cols, is_train=True)
    test_features = feature_engineering(test_df, fraction_cols, property_cols, is_train=False)
    
    # Prepare features and targets
    print("✔️ Preparing features and targets...")
    
    # Remove target columns from features
    feature_columns = [col for col in train_features.columns if col not in target_cols]
    X = train_features[feature_columns]
    y = train_features[target_cols]
    
    # Ensure test features have same columns as training features
    X_test = test_features[feature_columns]
    
    print(f"   - Feature matrix shape: {X.shape}")
    print(f"   - Target matrix shape: {y.shape}")
    print(f"   - Test matrix shape: {X_test.shape}")
    
    # Split training data
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=None
    )
    
    print(f"   - Training set: {X_train.shape[0]} samples")
    print(f"   - Validation set: {X_val.shape[0]} samples")
    
    # Train model
    model, target_mapes = train_model(X_train, y_train, X_val, y_val)
    
    # Feature importance analysis
    importance_df = extract_feature_importance(model, feature_columns)
    
    # Make predictions on test set
    print("✔️ Making predictions on test set...")
    test_predictions = model.predict(X_test)
    
    # Create submission file
    print("✔️ Creating submission file...")
    submission_df = pd.DataFrame()
    submission_df['ID'] = test_df['ID']
    
    for i, target_col in enumerate(target_cols):
        submission_df[target_col] = test_predictions[:, i]
    
    # Save submission
    submission_df.to_csv('submission.csv', index=False)
    print(f"   - Submission saved as 'submission.csv'")
    print(f"   - Submission shape: {submission_df.shape}")
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS SUMMARY")
    print("=" * 60)
    print(f"✅ Model trained successfully with {len(feature_columns)} features")
    print(f"✅ Average validation MAPE: {np.mean(target_mapes):.4f}")
    print(f"✅ Submission file created with {len(submission_df)} predictions")
    print(f"✅ Files generated: submission.csv, eda_analysis.png, feature_importance.png")
    
    # Display individual target performance
    print("\n📊 Individual Target Performance (Validation MAPE):")
    for i, (target, mape) in enumerate(zip(target_cols, target_mapes), 1):
        print(f"   {i:2d}. {target:<20} {mape:.4f}")
    
    print("\n🏆 Ready for Shell.ai Hackathon submission!")
    
    return model, submission_df, importance_df

if __name__ == "__main__":
    model, submission_df, importance_df = main()
