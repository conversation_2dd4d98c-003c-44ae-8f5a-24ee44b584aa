#!/usr/bin/env python3
"""
Choose Best Submission - Quick analysis of all submissions
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_percentage_error
import os

def calculate_mape_score(y_true, y_pred, reference_cost):
    """Calculate leaderboard score using MAPE and reference cost"""
    mape = mean_absolute_percentage_error(y_true, y_pred)
    score = max(10, 100 - (90 * mape / reference_cost))
    return score, mape

def analyze_submissions():
    """Analyze all available submissions"""
    print("🔍 Analyzing All Submissions to Find the BEST One")
    print("=" * 60)
    
    # Load training data for validation
    train_df = pd.read_csv('train.csv')
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    # Create validation split (same as used in training)
    _, X_val, _, y_val = train_test_split(
        train_df.drop(columns=target_cols), 
        train_df[target_cols], 
        test_size=0.2, 
        random_state=42
    )
    
    # Find all submission files
    submission_files = [f for f in os.listdir('.') if f.startswith('submission_') and f.endswith('.csv')]
    
    print(f"✔️ Found {len(submission_files)} submission files:")
    for f in submission_files:
        print(f"   - {f}")
    
    print("\n📊 Submission Analysis:")
    print("-" * 60)
    
    results = []
    
    for sub_file in submission_files:
        try:
            # Load submission
            sub_df = pd.read_csv(sub_file)
            
            # Basic validation
            if sub_df.shape[0] != 500:
                print(f"❌ {sub_file}: Wrong number of rows ({sub_df.shape[0]} instead of 500)")
                continue
                
            if sub_df.shape[1] != 11:
                print(f"❌ {sub_file}: Wrong number of columns ({sub_df.shape[1]} instead of 11)")
                continue
            
            # Check for missing values
            missing_count = sub_df.isnull().sum().sum()
            if missing_count > 0:
                print(f"⚠️  {sub_file}: Has {missing_count} missing values")
            
            # Check for infinite values
            inf_count = np.isinf(sub_df.select_dtypes(include=[np.number])).sum().sum()
            if inf_count > 0:
                print(f"⚠️  {sub_file}: Has {inf_count} infinite values")
            
            # Calculate basic statistics
            pred_values = sub_df[target_cols].values
            mean_pred = np.mean(pred_values)
            std_pred = np.std(pred_values)
            min_pred = np.min(pred_values)
            max_pred = np.max(pred_values)
            
            # Estimate quality based on reasonable ranges
            quality_score = 0
            if -5 <= min_pred <= 5 and -5 <= max_pred <= 5:  # Reasonable range
                quality_score += 1
            if 0.1 <= std_pred <= 2.0:  # Reasonable variance
                quality_score += 1
            if abs(mean_pred) <= 1.0:  # Reasonable mean
                quality_score += 1
            
            results.append({
                'file': sub_file,
                'mean': mean_pred,
                'std': std_pred,
                'min': min_pred,
                'max': max_pred,
                'quality': quality_score,
                'missing': missing_count,
                'infinite': inf_count
            })
            
            print(f"✅ {sub_file}:")
            print(f"   Mean: {mean_pred:.4f} | Std: {std_pred:.4f}")
            print(f"   Range: [{min_pred:.4f}, {max_pred:.4f}]")
            print(f"   Quality Score: {quality_score}/3")
            
        except Exception as e:
            print(f"❌ {sub_file}: Error loading - {str(e)}")
    
    # Rank submissions
    if results:
        print("\n🏆 SUBMISSION RANKING:")
        print("-" * 60)
        
        # Sort by quality score, then by reasonable statistics
        results.sort(key=lambda x: (x['quality'], -abs(x['mean']), x['std']), reverse=True)
        
        for i, result in enumerate(results, 1):
            status = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📊"
            print(f"{status} #{i}: {result['file']}")
            print(f"    Quality: {result['quality']}/3 | Mean: {result['mean']:.4f} | Std: {result['std']:.4f}")
        
        # Recommend best submission
        best_submission = results[0]['file']
        print(f"\n🎯 RECOMMENDED SUBMISSION: {best_submission}")
        print(f"   This submission has the highest quality score and most reasonable statistics.")
        
        # Show file sizes for additional info
        print(f"\n📁 File Sizes:")
        for result in results[:3]:  # Top 3
            try:
                size = os.path.getsize(result['file'])
                print(f"   {result['file']}: {size:,} bytes")
            except:
                pass
    
    else:
        print("❌ No valid submissions found!")
    
    print(f"\n💡 RECOMMENDATION:")
    print(f"   1. Use the top-ranked submission for your final entry")
    print(f"   2. If you have validation scores, prioritize those over statistics")
    print(f"   3. The 'ultra' submission was previously your best at 80.99%")
    
    return results

if __name__ == "__main__":
    results = analyze_submissions()
