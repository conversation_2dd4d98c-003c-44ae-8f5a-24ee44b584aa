#!/usr/bin/env python3
"""
Shell.ai Hackathon 2025: IMPROVED Fuel Blend Properties Prediction
Conservative improvements on the original 75% scoring solution

Key Improvements:
- Refined feature engineering (not excessive)
- Better hyperparameter tuning
- Improved model selection
- Enhanced validation approach
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_percentage_error
from sklearn.preprocessing import StandardScaler
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load the training, test, and sample solution data"""
    print("✔️ Loading data files...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_solution = pd.read_csv('sample_solution.csv')
    
    print(f"   - Training data shape: {train_df.shape}")
    print(f"   - Test data shape: {test_df.shape}")
    print(f"   - Sample solution shape: {sample_solution.shape}")
    
    return train_df, test_df, sample_solution

def improved_feature_engineering(df, fraction_cols, property_cols, is_train=True):
    """Improved feature engineering - conservative approach"""
    print("✔️ Applying improved feature engineering...")
    
    df_features = df.copy()
    
    # 1. Enhanced weighted averages (proven effective)
    print("   - Creating weighted features...")
    for prop_num in range(1, 11):
        weighted_sum = 0
        squared_weighted_sum = 0
        
        for comp_num in range(1, 6):
            fraction_col = f'Component{comp_num}_fraction'
            property_col = f'Component{comp_num}_Property{prop_num}'
            
            if fraction_col in df.columns and property_col in df.columns:
                # Linear weighting
                weighted_sum += df[fraction_col] * df[property_col]
                # Quadratic weighting (emphasizes dominant components)
                squared_weighted_sum += (df[fraction_col] ** 2) * df[property_col]
        
        df_features[f'WeightedAvg_Property{prop_num}'] = weighted_sum
        df_features[f'QuadWeightedAvg_Property{prop_num}'] = squared_weighted_sum
    
    # 2. Component statistics (key predictors)
    print("   - Creating component statistics...")
    df_features['Fraction_Variance'] = df[fraction_cols].var(axis=1)
    df_features['Fraction_Std'] = df[fraction_cols].std(axis=1)
    df_features['Fraction_Range'] = df[fraction_cols].max(axis=1) - df[fraction_cols].min(axis=1)
    
    # 3. Dominant component analysis
    df_features['Dominant_Component'] = df[fraction_cols].idxmax(axis=1).str.extract(r'(\d+)').astype(int)
    df_features['Max_Fraction'] = df[fraction_cols].max(axis=1)
    df_features['Second_Max_Fraction'] = df[fraction_cols].apply(lambda x: x.nlargest(2).iloc[1], axis=1)
    df_features['Dominance_Ratio'] = df_features['Max_Fraction'] / (df_features['Second_Max_Fraction'] + 1e-10)
    
    # 4. Component diversity (Shannon entropy)
    fractions_array = df[fraction_cols].values + 1e-10
    df_features['Shannon_Entropy'] = -np.sum(fractions_array * np.log(fractions_array), axis=1)
    
    # 5. Property statistics for key properties only
    print("   - Creating property statistics...")
    key_properties = [1, 2, 3, 5, 7, 8, 10]  # Most predictive based on analysis
    
    for prop_num in key_properties:
        prop_cols = [f'Component{i}_Property{prop_num}' for i in range(1, 6)]
        available_cols = [col for col in prop_cols if col in df.columns]
        
        if available_cols:
            df_features[f'Property{prop_num}_Mean'] = df[available_cols].mean(axis=1)
            df_features[f'Property{prop_num}_Std'] = df[available_cols].std(axis=1)
            df_features[f'Property{prop_num}_Range'] = df[available_cols].max(axis=1) - df[available_cols].min(axis=1)
    
    # 6. Key interactions (only most important ones)
    print("   - Creating key interactions...")
    # Most important interactions based on correlation analysis
    key_interactions = [
        (3, 7),  # Highest correlation: 0.997
        (1, 4),  # High correlation: 0.743
        (1, 2),  # High correlation: 0.726
        (2, 4),  # High correlation: 0.706
    ]
    
    for prop1, prop2 in key_interactions:
        df_features[f'WeightedAvg_P{prop1}_P{prop2}_Product'] = (
            df_features[f'WeightedAvg_Property{prop1}'] * df_features[f'WeightedAvg_Property{prop2}']
        )
        df_features[f'WeightedAvg_P{prop1}_P{prop2}_Ratio'] = (
            df_features[f'WeightedAvg_Property{prop1}'] / (df_features[f'WeightedAvg_Property{prop2}'] + 1e-10)
        )
    
    # 7. Component-specific interactions (limited to most important)
    important_components = [2, 5]  # Most predictive components
    important_properties = [1, 2, 5, 7]  # Most predictive properties
    
    for comp in important_components:
        fraction_col = f'Component{comp}_fraction'
        if fraction_col in df.columns:
            for prop in important_properties:
                property_col = f'Component{comp}_Property{prop}'
                if property_col in df.columns:
                    df_features[f'C{comp}_F_P{prop}_Interaction'] = df[fraction_col] * df[property_col]
    
    # 8. Blend characteristics
    df_features['Total_Blend_Volume'] = df[fraction_cols].sum(axis=1)
    df_features['Volume_Deviation'] = abs(df_features['Total_Blend_Volume'] - 1.0)
    df_features['Effective_Components'] = 1 / np.sum(df[fraction_cols].values ** 2, axis=1)
    
    print(f"   - Original features: {df.shape[1]}")
    print(f"   - Engineered features: {df_features.shape[1]}")
    print(f"   - New features added: {df_features.shape[1] - df.shape[1]}")
    
    return df_features

def create_improved_model():
    """Create improved LightGBM model with better hyperparameters"""
    print("✔️ Creating improved model...")
    
    # Improved LightGBM parameters based on analysis
    lgb_params = {
        'objective': 'regression',
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 25,  # Slightly reduced to prevent overfitting
        'learning_rate': 0.05,  # Slightly lower for better convergence
        'feature_fraction': 0.85,  # Slightly higher feature usage
        'bagging_fraction': 0.85,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'n_estimators': 400,  # Increased from 200
        'max_depth': 7,  # Slightly reduced depth
        'min_child_samples': 25,  # Increased to prevent overfitting
        'reg_alpha': 0.05,  # Reduced regularization
        'reg_lambda': 0.05,
        'min_split_gain': 0.01,
        'subsample_for_bin': 200000
    }
    
    return lgb.LGBMRegressor(**lgb_params)

def calculate_mape_score(y_true, y_pred, reference_cost):
    """Calculate leaderboard score using MAPE and reference cost"""
    mape = mean_absolute_percentage_error(y_true, y_pred)
    score = max(10, 100 - (90 * mape / reference_cost))
    return score, mape

def main():
    """Main execution function with improved approach"""
    print("🚀 Shell.ai Hackathon 2025: IMPROVED Fuel Blend Properties Prediction")
    print("=" * 75)
    
    # Load data
    train_df, test_df, sample_solution = load_data()
    
    # Identify feature columns
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    property_cols = [col for col in train_df.columns if 'Property' in col and 'Blend' not in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    print(f"✔️ Data overview:")
    print(f"   - Component fractions: {len(fraction_cols)}")
    print(f"   - Component properties: {len(property_cols)}")
    print(f"   - Blend properties (targets): {len(target_cols)}")
    
    # Improved Feature Engineering
    train_features = improved_feature_engineering(train_df, fraction_cols, property_cols, is_train=True)
    test_features = improved_feature_engineering(test_df, fraction_cols, property_cols, is_train=False)
    
    # Prepare features and targets
    print("✔️ Preparing features and targets...")
    
    # Remove target columns from features
    feature_columns = [col for col in train_features.columns if col not in target_cols]
    X = train_features[feature_columns]
    y = train_features[target_cols]
    
    # Ensure test features have same columns as training features
    X_test = test_features[feature_columns]
    
    print(f"   - Feature matrix shape: {X.shape}")
    print(f"   - Target matrix shape: {y.shape}")
    print(f"   - Test matrix shape: {X_test.shape}")
    
    # Handle missing values and infinite values
    print("✔️ Cleaning data...")
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    X_test = X_test.replace([np.inf, -np.inf], np.nan)
    X_test = X_test.fillna(X.median())
    
    # Split training data with better validation strategy
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=None
    )
    
    print(f"   - Training set: {X_train.shape[0]} samples")
    print(f"   - Validation set: {X_val.shape[0]} samples")
    
    # Create and train improved model
    lgb_model = create_improved_model()
    model = MultiOutputRegressor(lgb_model, n_jobs=2)
    
    print("✔️ Training improved model...")
    model.fit(X_train, y_train)
    
    # Make predictions on validation set
    print("✔️ Evaluating model performance...")
    y_val_pred = model.predict(X_val)
    
    # Calculate individual target performance
    target_mapes = []
    for i in range(y_val.shape[1]):
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_val_pred[:, i])
        target_mapes.append(mape)
    
    avg_mape = np.mean(target_mapes)
    print(f"   - Average validation MAPE: {avg_mape:.4f}")
    
    # Calculate leaderboard scores
    public_score, public_mape = calculate_mape_score(y_val.values, y_val_pred, 2.72)
    private_score, private_mape = calculate_mape_score(y_val.values, y_val_pred, 2.58)
    
    print(f"   - Public leaderboard score: {public_score:.2f} (MAPE: {public_mape:.4f})")
    print(f"   - Private leaderboard score: {private_score:.2f} (MAPE: {private_mape:.4f})")
    
    # Make predictions on test set
    print("✔️ Making predictions on test set...")
    test_predictions = model.predict(X_test)
    
    # Create submission file
    print("✔️ Creating improved submission file...")
    submission_df = pd.DataFrame()
    submission_df['ID'] = test_df['ID']
    
    for i, target_col in enumerate(target_cols):
        submission_df[target_col] = test_predictions[:, i]
    
    # Save submission
    submission_df.to_csv('submission_improved.csv', index=False)
    print(f"   - Improved submission saved as 'submission_improved.csv'")
    print(f"   - Submission shape: {submission_df.shape}")
    
    # Final summary
    print("\n" + "=" * 75)
    print("🎯 IMPROVED RESULTS SUMMARY")
    print("=" * 75)
    print(f"✅ Improved model trained with {X.shape[1]} features")
    print(f"✅ Validation MAPE: {avg_mape:.4f}")
    print(f"✅ Public score: {public_score:.2f} | Private score: {private_score:.2f}")
    print(f"✅ Improved submission created with {len(submission_df)} predictions")
    
    # Display individual target performance
    print("\n📊 Individual Target Performance (Validation MAPE):")
    for i, (target, mape) in enumerate(zip(target_cols, target_mapes), 1):
        if mape < 0.5:
            status = "🔥 EXCELLENT"
        elif mape < 1.0:
            status = "⚡ GREAT"
        elif mape < 1.5:
            status = "📈 GOOD"
        else:
            status = "🎯 FOCUS"
        print(f"   {i:2d}. {target:<20} {mape:.4f} {status}")
    
    # Compare with original performance
    original_score = 75.0  # Original score
    improvement = public_score - original_score
    
    print(f"\n💡 Performance Comparison:")
    print(f"   📊 Original score: {original_score:.1f}%")
    print(f"   🚀 Improved score: {public_score:.1f}%")
    print(f"   📈 Improvement: {improvement:+.1f} points")
    
    if improvement > 0:
        print(f"\n🏆 SUCCESS! Improved performance achieved!")
    else:
        print(f"\n⚠️  Performance regression detected. Consider reverting to original approach.")
    
    print(f"\n🏆 IMPROVED for Shell.ai Hackathon - Conservative Enhancement")
    
    return model, submission_df

if __name__ == "__main__":
    model, submission_df = main()
