#!/usr/bin/env python3
"""
Shell.ai Hackathon 2025: ENHANCED Fuel Blend Properties Prediction
Minimal enhancements to the original successful 75% scoring approach

Key Changes:
- Slightly improved hyperparameters
- Better validation approach
- Minimal additional features
- Preserved original successful architecture
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.multioutput import MultiOutputRegressor
from sklearn.metrics import mean_absolute_percentage_error
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_data():
    """Load the training, test, and sample solution data"""
    print("✔️ Loading data files...")
    
    train_df = pd.read_csv('train.csv')
    test_df = pd.read_csv('test.csv')
    sample_solution = pd.read_csv('sample_solution.csv')
    
    print(f"   - Training data shape: {train_df.shape}")
    print(f"   - Test data shape: {test_df.shape}")
    print(f"   - Sample solution shape: {sample_solution.shape}")
    
    return train_df, test_df, sample_solution

def enhanced_feature_engineering(df, fraction_cols, property_cols, is_train=True):
    """Enhanced feature engineering - minimal improvements to original"""
    print("✔️ Applying enhanced feature engineering...")
    
    df_features = df.copy()
    
    # 1. Weighted averages (original successful approach)
    print("   - Creating weighted features...")
    for prop_num in range(1, 11):
        weighted_sum = 0
        for comp_num in range(1, 6):
            fraction_col = f'Component{comp_num}_fraction'
            property_col = f'Component{comp_num}_Property{prop_num}'
            
            if fraction_col in df.columns and property_col in df.columns:
                weighted_sum += df[fraction_col] * df[property_col]
        
        df_features[f'WeightedAvg_Property{prop_num}'] = weighted_sum
    
    # 2. Component variance (original successful feature)
    df_features['Component_Variance'] = df[fraction_cols].var(axis=1)
    
    # 3. Dominant component (original successful feature)
    df_features['Dominant_Component'] = df[fraction_cols].idxmax(axis=1).str.extract(r'(\d+)').astype(int)
    
    # 4. MINIMAL additional features (only most predictive ones)
    df_features['Max_Fraction'] = df[fraction_cols].max(axis=1)
    df_features['Fraction_Range'] = df[fraction_cols].max(axis=1) - df[fraction_cols].min(axis=1)
    
    # 5. Only ONE key interaction (highest correlation: BlendProperty3 ↔ BlendProperty7)
    df_features['WeightedAvg_P3_P7_Product'] = (
        df_features['WeightedAvg_Property3'] * df_features['WeightedAvg_Property7']
    )
    
    # 6. Component-specific features for Component5 only (most predictive for BlendProperty1)
    if 'Component5_fraction' in df.columns:
        for prop in [1, 2, 5]:  # Only most important properties
            property_col = f'Component5_Property{prop}'
            if property_col in df.columns:
                df_features[f'C5_F_P{prop}'] = df['Component5_fraction'] * df[property_col]
    
    print(f"   - Original features: {df.shape[1]}")
    print(f"   - Enhanced features: {df_features.shape[1]}")
    print(f"   - New features added: {df_features.shape[1] - df.shape[1]}")
    
    return df_features

def train_enhanced_model(X_train, y_train, X_val, y_val):
    """Train enhanced model with slightly improved hyperparameters"""
    print("✔️ Training enhanced model...")
    
    # Enhanced LightGBM parameters (minimal changes from original)
    lgb_params = {
        'objective': 'regression',
        'metric': 'mae',
        'boosting_type': 'gbdt',
        'num_leaves': 20,  # Slightly increased from 15
        'learning_rate': 0.08,  # Slightly reduced from 0.1
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'n_estimators': 300,  # Increased from 200
        'max_depth': 6,  # Slightly reduced from default
        'min_child_samples': 20,
        'reg_alpha': 0.1,
        'reg_lambda': 0.1
    }
    
    # Create and train model
    lgb_model = lgb.LGBMRegressor(**lgb_params)
    model = MultiOutputRegressor(lgb_model, n_jobs=2)
    
    model.fit(X_train, y_train)
    
    # Evaluate on validation set
    y_val_pred = model.predict(X_val)
    
    # Calculate MAPE for each target
    target_mapes = []
    for i in range(y_val.shape[1]):
        mape = mean_absolute_percentage_error(y_val.iloc[:, i], y_val_pred[:, i])
        target_mapes.append(mape)
    
    avg_mape = np.mean(target_mapes)
    print(f"   - Average validation MAPE: {avg_mape:.4f}")
    
    # Calculate leaderboard scores
    public_mape = mean_absolute_percentage_error(y_val.values, y_val_pred)
    private_mape = public_mape  # Same for validation
    
    public_score = max(10, 100 - (90 * public_mape / 2.72))
    private_score = max(10, 100 - (90 * private_mape / 2.58))
    
    print(f"   - Public leaderboard score: {public_score:.2f}")
    print(f"   - Private leaderboard score: {private_score:.2f}")
    
    return model, target_mapes

def main():
    """Main execution function with enhanced approach"""
    print("🚀 Shell.ai Hackathon 2025: ENHANCED Fuel Blend Properties Prediction")
    print("=" * 70)
    
    # Load data
    train_df, test_df, sample_solution = load_data()
    
    # Identify feature columns
    fraction_cols = [col for col in train_df.columns if 'fraction' in col]
    property_cols = [col for col in train_df.columns if 'Property' in col and 'Blend' not in col]
    target_cols = [col for col in train_df.columns if 'BlendProperty' in col]
    
    print(f"✔️ Data overview:")
    print(f"   - Component fractions: {len(fraction_cols)}")
    print(f"   - Component properties: {len(property_cols)}")
    print(f"   - Blend properties (targets): {len(target_cols)}")
    
    # Enhanced Feature Engineering
    train_features = enhanced_feature_engineering(train_df, fraction_cols, property_cols, is_train=True)
    test_features = enhanced_feature_engineering(test_df, fraction_cols, property_cols, is_train=False)
    
    # Prepare features and targets
    print("✔️ Preparing features and targets...")
    
    # Remove target columns from features
    feature_columns = [col for col in train_features.columns if col not in target_cols]
    X = train_features[feature_columns]
    y = train_features[target_cols]
    
    # Ensure test features have same columns as training features
    X_test = test_features[feature_columns]
    
    print(f"   - Feature matrix shape: {X.shape}")
    print(f"   - Target matrix shape: {y.shape}")
    print(f"   - Test matrix shape: {X_test.shape}")
    
    # Split training data (same as original)
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=None
    )
    
    print(f"   - Training set: {X_train.shape[0]} samples")
    print(f"   - Validation set: {X_val.shape[0]} samples")
    
    # Train enhanced model
    model, target_mapes = train_enhanced_model(X_train, y_train, X_val, y_val)
    
    # Make predictions on test set
    print("✔️ Making predictions on test set...")
    test_predictions = model.predict(X_test)
    
    # Create submission file
    print("✔️ Creating enhanced submission file...")
    submission_df = pd.DataFrame()
    submission_df['ID'] = test_df['ID']
    
    for i, target_col in enumerate(target_cols):
        submission_df[target_col] = test_predictions[:, i]
    
    # Save submission
    submission_df.to_csv('submission_enhanced.csv', index=False)
    print(f"   - Enhanced submission saved as 'submission_enhanced.csv'")
    print(f"   - Submission shape: {submission_df.shape}")
    
    # Final summary
    print("\n" + "=" * 70)
    print("🎯 ENHANCED RESULTS SUMMARY")
    print("=" * 70)
    print(f"✅ Enhanced model trained with {len(feature_columns)} features")
    print(f"✅ Average validation MAPE: {np.mean(target_mapes):.4f}")
    print(f"✅ Enhanced submission created with {len(submission_df)} predictions")
    
    # Display individual target performance
    print("\n📊 Individual Target Performance (Validation MAPE):")
    for i, (target, mape) in enumerate(zip(target_cols, target_mapes), 1):
        if mape < 1.0:
            status = "🔥 EXCELLENT"
        elif mape < 1.5:
            status = "⚡ GREAT"
        elif mape < 2.0:
            status = "📈 GOOD"
        else:
            status = "🎯 FOCUS"
        print(f"   {i:2d}. {target:<20} {mape:.4f} {status}")
    
    # Compare with original
    original_mape = 0.9321  # From original run
    current_mape = np.mean(target_mapes)
    
    print(f"\n💡 Performance Comparison:")
    print(f"   📊 Original MAPE: {original_mape:.4f}")
    print(f"   🚀 Enhanced MAPE: {current_mape:.4f}")
    
    if current_mape < original_mape:
        improvement = ((original_mape - current_mape) / original_mape) * 100
        print(f"   📈 Improvement: {improvement:.1f}% better")
        print(f"\n🏆 SUCCESS! Enhanced performance achieved!")
    else:
        regression = ((current_mape - original_mape) / original_mape) * 100
        print(f"   📉 Regression: {regression:.1f}% worse")
        print(f"\n⚠️  Consider using original submission if this performs worse")
    
    print(f"\n🏆 ENHANCED for Shell.ai Hackathon - Minimal Improvements")
    
    return model, submission_df

if __name__ == "__main__":
    model, submission_df = main()
