#!/usr/bin/env python3
"""
Validation script for Shell.ai Hackathon submission
"""

import pandas as pd

def validate_submission():
    """Validate the submission file format"""
    print("🔍 Validating submission file...")
    
    # Load files
    submission = pd.read_csv('submission.csv')
    test = pd.read_csv('test.csv')
    
    # Validation checks
    checks = []
    
    # Check number of rows
    rows_ok = len(submission) == 500
    checks.append(("Rows (500)", rows_ok, len(submission)))
    
    # Check number of columns
    cols_ok = len(submission.columns) == 11
    checks.append(("Columns (11)", cols_ok, len(submission.columns)))
    
    # Check ID matching
    id_match = (submission.ID == test.ID).all()
    checks.append(("ID matching", id_match, "✓" if id_match else "✗"))
    
    # Check for missing values
    no_missing = submission.isnull().sum().sum() == 0
    checks.append(("No missing values", no_missing, submission.isnull().sum().sum()))
    
    # Check column names
    expected_cols = ['ID'] + [f'BlendProperty{i}' for i in range(1, 11)]
    cols_correct = list(submission.columns) == expected_cols
    checks.append(("Column names", cols_correct, "✓" if cols_correct else "✗"))
    
    # Check data types (all predictions should be numeric, excluding ID)
    prediction_cols = [col for col in submission.columns if col != 'ID']
    numeric_cols = submission[prediction_cols].select_dtypes(include=['number']).shape[1]
    numeric_ok = numeric_cols == 10  # All blend properties should be numeric
    checks.append(("Numeric predictions", numeric_ok, f"{numeric_cols}/10"))
    
    # Print results
    print("\n📋 Validation Results:")
    print("-" * 50)
    all_passed = True
    for check_name, passed, value in checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check_name:<20} {status:<8} ({value})")
        if not passed:
            all_passed = False
    
    print("-" * 50)
    if all_passed:
        print("🎉 All validation checks PASSED! Ready for submission!")
    else:
        print("⚠️  Some validation checks FAILED. Please review.")
    
    # Additional info
    print(f"\n📊 Submission Summary:")
    print(f"   - Shape: {submission.shape}")
    print(f"   - ID range: {submission.ID.min()} to {submission.ID.max()}")
    print(f"   - Prediction ranges:")
    for col in submission.columns[1:]:  # Skip ID column
        min_val = submission[col].min()
        max_val = submission[col].max()
        print(f"     {col}: [{min_val:.4f}, {max_val:.4f}]")

if __name__ == "__main__":
    validate_submission()
